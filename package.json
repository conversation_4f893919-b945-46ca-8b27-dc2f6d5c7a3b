{"name": "@fuse/demo", "version": "14.2.0", "description": "Fuse - Angular Admin Template and Starter Project", "author": "https://themeforest.net/user/srcn", "license": "https://themeforest.net/licenses/standard", "private": true, "scripts": {"ng": "ng", "start": "ng serve --host 0.0.0.0", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint"}, "dependencies": {"@angular/animations": "19.2.10", "@angular/cdk": "19.2.15", "@angular/common": "19.2.10", "@angular/compiler": "19.2.10", "@angular/core": "19.2.10", "@angular/forms": "19.2.10", "@angular/material": "19.2.15", "@angular/material-luxon-adapter": "19.0.4", "@angular/material-moment-adapter": "19.2.15", "@angular/platform-browser": "19.2.10", "@angular/platform-browser-dynamic": "19.2.10", "@angular/router": "19.2.10", "@fortawesome/angular-fontawesome": "^0.10.0", "@fortawesome/fontawesome-free": "^6.0.0", "@fortawesome/fontawesome-svg-core": "^1.2.36", "@fortawesome/free-solid-svg-icons": "^5.15.4", "@fullcalendar/angular": "^6.1.17", "@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@fullcalendar/web-component": "^6.1.17", "@line/liff": "^2.26.0", "@ngneat/transloco": "4.1.1", "@types/luxon": "^3.6.2", "@zxcvbn-ts/core": "^2.0.5", "@zxcvbn-ts/language-en": "^2.1.0", "angular-datatables": "^14.0.0", "apexcharts": "^3.35.5", "chart.js": "^3.9.1", "crypto-js": "^4.2.0", "datatables.net": "^1.11.3", "datatables.net-dt": "^1.11.3", "file-saver": "^2.0.5", "fullcalendar": "^5.11.5", "highlight.js": "11.6.0", "jquery": "^3.7.1", "jsbarcode": "^3.11.6", "lodash-es": "4.17.21", "luxon": "^3.5.0", "marked": "^15.0.11", "moment": "2.29.4", "ng-apexcharts": "^1.7.1", "ng-drag-drop": "^5.0.0", "ng-image-slider": "^10.0.0", "ngx-dropzone": "^3.1.0", "ngx-formly-designer": "0.0.6", "ngx-image-viewer": "^1.0.13", "ngx-markdown": "19.1.1", "ngx-mask": "^14.3.3", "ngx-mat-select-search": "^8.0.2", "ngx-mat-timepicker": "^19.0.0", "ngx-quill": "26.0.10", "perfect-scrollbar": "1.5.5", "qrious": "^4.0.2", "quill": "2.0.3", "rxjs": "7.5.6", "sweetalert2": "^11.6.13", "tslib": "2.4.0", "xlsx": "^0.18.5", "zone.js": "0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.11", "@angular-eslint/builder": "18.4.3", "@angular-eslint/eslint-plugin": "18.4.3", "@angular-eslint/eslint-plugin-template": "18.4.3", "@angular-eslint/schematics": "18.4.3", "@angular-eslint/template-parser": "18.4.3", "@angular/cli": "19.2.11", "@angular/compiler-cli": "19.2.10", "@tailwindcss/aspect-ratio": "0.4.2", "@tailwindcss/line-clamp": "0.4.2", "@tailwindcss/typography": "0.5.7", "@types/chroma-js": "2.1.4", "@types/crypto-js": "4.1.1", "@types/datatables.net": "^1.10.21", "@types/highlight.js": "10.1.0", "@types/jquery": "^3.5.9", "@types/lodash": "4.14.185", "@types/lodash-es": "4.17.6", "@types/node": "^18.7.18", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "autoprefixer": "10.4.11", "chroma-js": "2.4.2", "eslint": "^8.57.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-jsdoc": "39.3.6", "eslint-plugin-prefer-arrow": "1.2.3", "jasmine-core": "4.4.0", "lodash": "^4.17.21", "postcss": "^8.5.3", "tailwindcss": "3.1.8", "typescript": "5.8.3"}}