import { Component, input } from '@angular/core';
import { FormsModule, ReactiveFormsModule, FormGroup, FormControl, Validators } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { DataTablesModule } from 'angular-datatables';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { CommonModule } from '@angular/common';
import liff from '@line/liff';
import { catchError, filter, of, Subscription } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { CanCheckInComponent } from './can-check-in/can-check-in.component';
import { AfterViewInit, OnInit } from '@angular/core';
import { resizeAndConvertToBase64 } from 'app/helper';
import { DateTime } from 'luxon';
import { environment } from 'environments/environment';
import { FuseConfirmationModule, FuseConfirmationService } from '@fuse/services/confirmation';
import { LineService } from '../line.service';
import { NavigationEnd, Router } from '@angular/router';

declare namespace longdo {
    class Map {
        constructor(options: any);
        location(coord: { lon: number; lat: number }, animate?: boolean): void;
        Overlays: {
            add(overlay: any): void;
        };
    }

    class Marker {
        constructor(location: { lon: number; lat: number });
    }
}

@Component({
    selector: 'app-check-in',
    standalone: true,
    imports: [FormsModule, ReactiveFormsModule, MatIcon, CanCheckInComponent,
        DataTablesModule, MatFormFieldModule, MatInputModule, CommonModule],
    templateUrl: './check-in.component.html',
    styleUrl: './check-in.component.scss'
})
export class CheckInComponent implements OnInit, AfterViewInit {

    profile: any;
    liffInitialized = false;
    selectedFileName = ''

    form = new FormGroup({
        line_id: new FormControl('', Validators.required),
        latitude: new FormControl<number>(null, Validators.required),
        longitude: new FormControl<number>(null, Validators.required),
        address: new FormControl('', Validators.required),
        pic: new FormControl<any>(null, Validators.required),
        time: new FormControl('', Validators.required),
    });

    previewImage: string = null;

    map: any;
    place = '';

    private navSub?: Subscription;
    private mapReady = false;

    constructor(
        private _service: LineService,
        private dialog: MatDialog,
        private _router: Router,
        private _fuseConfirmationService: FuseConfirmationService,
    ) { }

    async ngOnInit() {
        try {
            await liff.init({ liffId: environment.LIFF_CHECK_IN });
            if (liff.isLoggedIn()) {
                this.profile = await liff.getProfile();
                this.form.get('line_id')?.setValue(this.profile?.userId);

                this.loadmap(); // เตรียมแผนที่
                await this.requestLocationFlow(); // << ขอสิทธิ + ขอพิกัดครั้งแรกทันที
            } else {
                liff.login();
            }
        } catch (error) {
            console.error('LIFF initialization failed', error);
        }

        // เมื่อมีการนำทางกลับมาหน้านี้อีก ให้ขอพิกัดใหม่เสมอ
        this.navSub = this._router.events
            .pipe(filter(e => e instanceof NavigationEnd))
            .subscribe(() => {
                this.requestLocationFlow();
            });
    }

    ngOnDestroy(): void {
        this.navSub?.unsubscribe();
        document.removeEventListener('visibilitychange', () => { });
    }

    ngAfterViewInit(): void {
        // กลับมาจากพื้นหลัง/แท็บอื่น ให้ขอพิกัดใหม่
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                this.requestLocationFlow();
            }
        });
    }

    onLocationFound(placeName: string) {
        this.form.get('address')?.setValue(placeName);
    };

    onFileSelected(event: Event) {
        const input = event.target as HTMLInputElement;
        if (!input.files || input.files.length === 0) return;

        const file = input.files[0];

        resizeAndConvertToBase64(file).then((base64: string) => {
            this.previewImage = base64;

            this.form.patchValue({
                pic: base64
            });
        }).catch(err => {
            console.error(err);
        });
    }

    close() {
        liff.closeWindow();
    }

    // === flow หลักในการขอ location ทุกครั้งที่ "เปิดหน้า" ===
    private async requestLocationFlow() {
        try {
            // ถ้า map ยังไม่พร้อม ให้เตรียมก่อน
            if (!this.mapReady) this.loadmap();

            // ใช้ Permissions API เพื่อตรวจสอบสถานะ
            const perm = await (navigator as any).permissions?.query?.({ name: 'geolocation' as PermissionName });

            if (!perm || perm.state === 'prompt') {
                // จะเด้งถามสิทธิทุกครั้งที่ยังไม่ Granted
                this.pinCurrentLocation();
            } else if (perm.state === 'granted') {
                // เคยอนุญาตแล้ว ก็ยิงขอพิกัดใหม่ทุกครั้ง
                this.pinCurrentLocation();
            } else {
                // denied
                this.openNativeConfirmDialog(); // แจ้งผู้ใช้ให้เปิด Location ใน Settings/เบราว์เซอร์
            }
        } catch {
            // fallback ถ้า Permissions API ใช้ไม่ได้
            this.pinCurrentLocation();
        }
    }

    loadmap() {
        this.map = new longdo.Map({
            placeholder: document.getElementById('map'),
            ui: false,
            draggable: false,
            scrollWheel: false,
            touch: false,
            keyboard: false,
            zoom: 16,
            zoomRange: { min: 16, max: 16 },
            lastView: false,
            Renderer: false,
            input: false,
        });
        this.map.Ui.Crosshair.visible(false);
        this.mapReady = true;
    }


    pinCurrentLocation(): void {
        navigator.geolocation.getCurrentPosition(
            async (pos) => {
                const lat = pos.coords.latitude;
                const lon = pos.coords.longitude;

                this._service.canCheckIn(lat, lon).subscribe((can) => {
                    if (can.data.can_check_in === true) {
                        this.map.location({ lon, lat }, true);
                        this.map.Overlays.add(new longdo.Marker({ lon, lat }));

                        this._service
                            .reverseGeocode(lat, lon)
                            .pipe(
                                catchError((err) => {
                                    console.error('Reverse geocoding failed', err);
                                    return of('unknown place');
                                })
                            )
                            .subscribe((placeName) => {
                                const time = DateTime.local().toFormat('yyyy-MM-dd HH:mm:ss');
                                this.form.patchValue({
                                    latitude: lat,
                                    longitude: lon,
                                    time: time,
                                });
                                this.form.get('address')?.setValue(placeName || 'unknown place');
                            });
                    } else {
                        this.openNativeConfirmDialog();
                    }
                });
            },
            (error) => {
                console.error('Geolocation error:', error);
                this.form.get('address')?.setValue('unknown place');
            },
            {
                enableHighAccuracy: true,
                timeout: 15000,
                maximumAge: 0, // สำคัญ: บังคับให้ดึงพิกัดใหม่ทุกครั้ง (ไม่ใช้ cache)
            }
        );
    }
    openNativeConfirmDialog(): void {
        const dialogRef = this.dialog.open(CanCheckInComponent, {
            width: '400px',
            height: 'fit-content',
        });

        dialogRef.afterClosed().subscribe(result => {
            if (result === 'ok') { // สมมติว่าคุณส่งค่า 'ok' กลับจากปุ่มตกลง
                liff.closeWindow();
            }
        });
    }

    submit() {
        if (this.form.invalid) {
            this.form.updateValueAndValidity();
            return;
        }

        this._service.checkIn(this.form.value).subscribe({
            next: (resp: any) => {
                this._fuseConfirmationService.open({
                    title: 'สำเร็จ',
                    message: 'บันทึกสำเร็จ',
                    icon: {
                        show: true,
                        name: 'heroicons_outline:check-circle',
                        color: 'success',
                    },
                    actions: {
                        confirm: {
                            show: true,
                            label: 'ตกลง',
                            color: 'primary',
                        },
                        cancel: {
                            show: false,
                        },
                    },
                }).afterClosed().subscribe(() => {
                    liff.closeWindow();
                });
            },
            error: (err: any) => {
                this._fuseConfirmationService.open({
                    title: 'เกิดข้อผิดพลาด',
                    message: err?.error?.message,
                    icon: {
                        show: true,
                        name: 'heroicons_outline:exclamation-triangle',
                        color: 'warning',
                    },
                    actions: {
                        confirm: {
                            show: true,
                            label: 'ตกลง',
                            color: 'primary',
                        },
                        cancel: {
                            show: false,
                        },
                    },
                    dismissible: true,
                })
            }
        })
    }
}
