import { Component } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-can-check-in',
  imports: [MatIconModule],
  templateUrl: './can-check-in.component.html',
})
export class CanCheckInComponent {
    constructor(private dialogRef: MatDialogRef<CanCheckInComponent>) {}
    close() {
        this.dialogRef.close('ok');
    }
}
