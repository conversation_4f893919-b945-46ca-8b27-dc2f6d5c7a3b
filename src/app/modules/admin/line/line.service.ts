import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'environments/environment';
import { BehaviorSubject, catchError, map, Observable, of, switchMap, tap } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class LineService {
  private _materials: BehaviorSubject<any[] | null> = new BehaviorSubject(
    null
  );
  constructor(
    private readonly _httpClient: HttpClient,
  ) { }

  createOt(itemtype: any): Observable<any> {
    return this._httpClient.post(environment.API_URL + 'api/ot_line', itemtype,);
  }

  getTypeOt(lineId: string) {
    return this._httpClient.get<any[]>(environment.API_URL + 'api/get_ot_type_line', {
      params: { line_id: lineId },
    });
  }

  createLeave(data: any): Observable<any> {
    return this._httpClient.post<any>(environment.API_URL + 'api/leave_line', data);
  }

  getTypeLeavenew(lineId: string): Observable<any[]> {
    return this._httpClient.get<any[]>(environment.API_URL + 'api/get_user_leave_permission_line', {
      params: { line_id: lineId },
    });
  }

  uploadFile(item: FormData): Observable<any> {
    return this._httpClient.post<any>(environment.API_URL + 'api/upload_file', item,);
  }

  apiKey = environment.LONG_DO_API_KEY;
  count = 0;

  reverseGeocode(lat: number, lon: number): Observable<string> {
    const url = `https://api.longdo.com/map/services/address?lon=${lon}&lat=${lat}&noelevation=1&key=${this.apiKey}`;

    return this._httpClient.get<any>(url).pipe(
      catchError((error) => {
        console.error(this.count + 'Error caught:', error);
        this.count += 1;
        if (this.count < 20) {
          return this.reverseGeocode(lat, lon)
        } return "can't found place"
      }),
      map(data => {
        // return `${data.data[0].name}`.trim();
        if (data?.aoi) return data.aoi;
        const road = data?.road || '';
        const subdistrict = data?.subdistrict || '';
        const district = data?.district || '';
        const province = data?.province || '';
        const country = data?.country || '';
        const postcode = data?.postcode || '';
        return `${road} ${subdistrict} ${district} ${province} ${country} ${postcode}`.trim();
      }),
    );
  }



  canCheckIn(lat: any, lon: any): Observable<any> {
    return this._httpClient.get(
      `${environment.API_URL}api/can_check_in?latitude=${lat}&longitude=${lon}`)
  }

  checkIn(data: any): Observable<any> {
    return this._httpClient.post(
      `${environment.API_URL}api/put_zk_time`, data)
  }

  approveLeave(id: any, data: any): Observable<any> {
    return this._httpClient.put(environment.API_URL + 'api/approved_leave_table_line/' + id, data,);
  }

  submitWarning(id: any, data: any): Observable<any> {
    return this._httpClient.put(environment.API_URL + 'api/approved_warning_line/' + id, data,);
  }

  approveWarning(data: any, itemId: any): Observable<any[]> {
    return this._httpClient
      .put(
        environment.API_URL + `api/update_warning_line/${itemId}`,
        data,
      )
      .pipe(
        switchMap((response: any) => {
          // Return a new observable with the response
          return of(response);
        })
      );
  }

  

   approveWarningStaff(data: any, itemId: any): Observable<any[]> {
    return this._httpClient
      .put(
        environment.API_URL + `api/update_warning_line/${itemId}`,
        data,
      )
      .pipe(
        switchMap((response: any) => {
          // Return a new observable with the response
          return of(response);
        })
      );
  }

  approveOt(id: any, data: any): Observable<any> {
    return this._httpClient.put(environment.API_URL + 'api/approved_ot_line/' + id, data,);
  }

  getUserprofile(lineId: string): Observable<any> {
    return this._httpClient
      .get(environment.API_URL + 'api/user_profile_line', {
        params: {
          line_id: lineId
        }
      })
  }

}
