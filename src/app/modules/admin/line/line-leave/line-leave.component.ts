import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    inject,
    <PERSON><PERSON><PERSON><PERSON>,
    OnInit,
} from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, ValidationErrors, Validators } from '@angular/forms';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import moment from 'moment';
import { CommonModule, Location, NgFor } from '@angular/common';
import { MatFormField, MatFormFieldModule } from '@angular/material/form-field';
import { MatSelect, MatSelectModule } from '@angular/material/select';
import { MatOption } from '@angular/material/autocomplete';
import { NgxDropzoneModule } from 'ngx-dropzone';
import { MatButton, MatButtonModule } from '@angular/material/button';
import { MatIcon, MatIconModule } from '@angular/material/icon';
import { LineService } from '../line.service';
import { environment } from 'environments/environment';
import liff from '@line/liff';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { DateTime } from 'luxon';
import { MatCardModule } from '@angular/material/card';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatInputModule } from '@angular/material/input';
import { MatSnackBar } from '@angular/material/snack-bar';
import { merge } from 'rxjs';
// import { ImportOSMComponent } from '../card/import-osm/import-osm.component';

type LeaveTypeOption = { leave_type_id: number; leave_type?: { name: string } };
type LeaveFormType = 'day' | 'half_day_pm' | 'half_day_am' | 'hour';

@Component({
    selector: 'line-leave-leave',
    templateUrl: './line-leave.component.html',
    styleUrls: ['./line-leave.component.scss'],
    standalone: true,
    // animations: fuseAnimations,
    changeDetection: ChangeDetectionStrategy.Default,
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        NgxDropzoneModule,
        MatNativeDateModule,
        MatFormFieldModule,
        MatSelectModule,
        MatButtonModule,
        MatIconModule,
        MatCardModule,
        MatDatepickerModule,
        MatToolbarModule,
        MatInputModule
    ]
})
export class LineLeaveComponent implements OnInit, AfterViewInit, OnDestroy {

    selectedTime: Date;
    formData: FormGroup;
    uploadData: FormData;
    flashErrorMessage: string;
    flashMessage: 'success' | 'error' | null = null;
    isLoading: boolean = false;
    leaveType: any = [];
    files: File[] = [];

    profile: any;
    liffInitialized = false;

    type: any = [
        { label: 'ทั้งวัน', value: 'day' },
        { label: 'ครึ่งวันเช้า', value: 'half_day_pm' },
        { label: 'ครึ่งวันบ่าย', value: 'half_day_am' },
    ]

    // รูปแบบการลา
    typeOptions = [
        { value: 'day' as LeaveFormType, label: 'เต็มวัน (หลายวันได้)' },
        { value: 'half_day_am' as LeaveFormType, label: 'ครึ่งวันเช้า' },
        { value: 'half_day_pm' as LeaveFormType, label: 'ครึ่งวันบ่าย' },
        { value: 'hour' as LeaveFormType, label: 'รายชั่วโมง' },
    ];

    readonly zone = 'Asia/Bangkok';
    timeOptions = this.buildTimeSlots(); // ["08:00", "08:30", ...]
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseConfirmationService: FuseConfirmationService,
        private _formBuilder: FormBuilder,
        private _Service: LineService,
    ) {

    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    async ngOnInit(): Promise<void> {
        const today = DateTime.now().setZone(this.zone).startOf('day');
        this.formData = this._formBuilder.group(
            {
                line_id: [''],
                leave_type_id: [null, Validators.required],
                type: ['day' as LeaveFormType, Validators.required],

                // สำหรับแบบเต็มวัน (ช่วงวันที่)
                date_start: [null],
                date_end: [null],

                // สำหรับแบบวันเดียว (ครึ่งวัน/รายชั่วโมง)
                date: [null],

                // สำหรับรายชั่วโมง
                time_start: [null],
                time_end: [null],

                description: ['', Validators.maxLength(500)],
                file: [''],
            },
            { validators: [this.leaveValidator] } // วาง cross-field validator ไว้ที่ form
        );

        // ปรับ required ของคอนโทรลให้เหมาะกับ type ปัจจุบัน
        this.formData.get('type')!.valueChanges.subscribe(() => this.syncControlRequirements());

        this.formData.get('type')!.valueChanges.subscribe(() => {
            this.clearInvalidTimeRangeError();
        });

        // ฟังการเปลี่ยนของ time_start/time_end เฉพาะตอนเป็นรายชั่วโมง
        const tStart = this.formData.get('time_start')!;
        const tEnd = this.formData.get('time_end')!;

        merge(tStart.valueChanges, tEnd.valueChanges).subscribe(() => {
            if (this.formData.get('type')?.value !== 'hour') return;

            const start = tStart.value as string | null;
            const end = tEnd.value as string | null;

            // มีค่าทั้งคู่แล้วค่อยเช็ค
            if (start && end) {
                const valid = this.toMinutes(start) < this.toMinutes(end);
                if (!valid) {
                    // ตั้ง error ระดับฟอร์ม + รีเซ็ตค่าสิ้นสุด (จะเลือกใหม่)
                    this.formData.setErrors({ ...(this.formData.errors ?? {}), invalidTimeRange: true });
                    tEnd.reset();

                    // แจ้งเตือน
                    // this.snack.open('เวลาสิ้นสุดต้องหลังเวลาเริ่ม', 'ปิด', {
                    //     duration: 3000,
                    //     panelClass: ['snack-error'] // ชื่อ class ที่เราสร้างเอง
                    // });
                    // ทำให้ field ถูกแตะ/แสดง error ทันที
                    tEnd.markAsTouched();
                    tEnd.markAsDirty();
                } else {
                    this.clearInvalidTimeRangeError();
                }
            }
        });



        this.syncControlRequirements();
        this.generateTimeOptions()
        // this.getTypeLeave('U7f8d3bed4f907ec2b4bd88d5a40d8937');
        try {
            await liff.init({ liffId: environment.LIFF_LEAVE });
            this.liffInitialized = true;

            if (liff.isLoggedIn()) {

                this.profile = await liff.getProfile();

                this.formData.get('line_id')?.setValue(this.profile?.userId);

                this.getTypeLeave(this.profile?.userId);
            } else {
                liff.login();
            }
        } catch (error) {
            console.error('LIFF initialization failed', error);
        }

        // ฟังการเปลี่ยนแปลง date_start
        this.formData.get('date_start')?.valueChanges.subscribe(value => {
            if (value instanceof Date) {
                const formatted = DateTime.fromJSDate(value).toFormat('yyyy-MM-dd');
                this.formData.patchValue({ date_start: formatted }, { emitEvent: false });
            }
        });

        // ฟังการเปลี่ยนแปลง date_end
        this.formData.get('date_end')?.valueChanges.subscribe(value => {
            if (value instanceof Date) {
                const formatted = DateTime.fromJSDate(value).toFormat('yyyy-MM-dd');
                this.formData.patchValue({ date_end: formatted }, { emitEvent: false });
            }
        });

        this.formData.get('date')?.valueChanges.subscribe(value => {
            if (value instanceof Date) {
                const formatted = DateTime.fromJSDate(value).toFormat('yyyy-MM-dd');
                this.formData.patchValue({ date: formatted }, { emitEvent: false });
            }
        });
    }

    private snack = inject(MatSnackBar);

    private toMinutes = (t: string) => {
        const [h, m] = t.split(':').map(Number);
        return h * 60 + m;
    };


    // === Cross-field validator ครอบคลุมทุกเงื่อนไข ===
    private leaveValidator = (group: AbstractControl): ValidationErrors | null => {
        const type = group.get('type')?.value as LeaveFormType;

        const date_start = group.get('date_start')?.value as Date | null;
        const date_end = group.get('date_end')?.value as Date | null;
        const date = group.get('date')?.value as Date | null;

        const time_start = group.get('time_start')?.value as string | null;
        const time_end = group.get('time_end')?.value as string | null;

        const errors: ValidationErrors = {};

        // 1) ถ้าเต็มวัน: ต้องมี start/end และ start <= end
        if (type === 'day') {
            if (!date_start || !date_end) {
                errors['invalidRange'] = true;
            } else {
                const s = new Date(date_start).setHours(0, 0, 0, 0);
                const e = new Date(date_end).setHours(0, 0, 0, 0);
                if (s > e) errors['invalidRange'] = true;
            }
        }

        // 2) ถ้าไม่ใช่เต็มวัน: ต้องเลือกวันเดียว
        if (type !== 'day') {
            if (!date) {
                errors['singleDateRequired'] = true;
            }
        }

        // 3) ถ้ารายชั่วโมง: ต้องมีเวลาเริ่ม/สิ้นสุด และ start < end
        if (type === 'hour') {
            if (!time_start || !time_end) {
                errors['invalidTimeRange'] = true;
            } else {
                if (!this.isStartBeforeEnd(time_start, time_end)) {
                    errors['invalidTimeRange'] = true;
                }
            }
        }

        return Object.keys(errors).length ? errors : null;
    };

    // ปรับ required ของคอนโทรลให้ตรงกับ type + เคลียร์ค่าที่ไม่ใช้
    private syncControlRequirements(): void {
        const type = this.formData.get('type')?.value as LeaveFormType;

        const date_start = this.formData.get('date_start')!;
        const date_end = this.formData.get('date_end')!;
        const date = this.formData.get('date')!;
        const time_start = this.formData.get('time_start')!;
        const time_end = this.formData.get('time_end')!;

        // reset validators
        date_start.clearValidators();
        date_end.clearValidators();
        date.clearValidators();
        time_start.clearValidators();
        time_end.clearValidators();

        // apply by type
        if (type === 'day') {
            date_start.setValidators([Validators.required]);
            date_end.setValidators([Validators.required]);
            // เคลียร์ของ single-day / hour
            date.reset();
            time_start.reset();
            time_end.reset();
        } else {
            date.setValidators([Validators.required]);
            // เคลียร์ช่วงวันที่
            date_start.reset();
            date_end.reset();

            if (type === 'hour') {
                time_start.setValidators([Validators.required]);
                time_end.setValidators([Validators.required]);
            } else {
                time_start.reset();
                time_end.reset();
            }
        }

        date_start.updateValueAndValidity({ emitEvent: false });
        date_end.updateValueAndValidity({ emitEvent: false });
        date.updateValueAndValidity({ emitEvent: false });
        time_start.updateValueAndValidity({ emitEvent: false });
        time_end.updateValueAndValidity({ emitEvent: false });

        // กระตุ้นให้ form-level validator ตรวจใหม่
        this.formData.updateValueAndValidity({ emitEvent: false });
    }

    private buildTimeOptions(): string[] {
        const opts: string[] = [];
        for (let h = 0; h < 24; h++) {
            for (let m of [0, 30]) {
                const hh = String(h).padStart(2, '0');
                const mm = String(m).padStart(2, '0');
                opts.push(`${hh}:${mm}`);
            }
        }
        return opts;
    }

    private isStartBeforeEnd(start: string, end: string): boolean {
        // "HH:mm" -> เปรียบเทียบเป็นนาที
        const toMin = (t: string) => {
            const [h, m] = t.split(':').map(Number);
            return h * 60 + m;
        };
        return toMin(start) < toMin(end);
    }

    private clearInvalidTimeRangeError() {
        const errs = { ...(this.formData.errors ?? {}) };
        if ('invalidTimeRange' in errs) {
            delete errs['invalidTimeRange'];
            this.formData.setErrors(Object.keys(errs).length ? errs : null);
        }
    }

    // ====== อื่น ๆ ตามเดิม ======

    discard(): void { }

    /**
     * After view init
     */
    ngAfterViewInit(): void { }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
    }

    newLeave(): void {

        const confirmation = this._fuseConfirmationService.open({
            title: 'แจ้งลา',
            message: 'คุณต้องการแจ้งลาใช่หรือไม่ ?',
            icon: {
                show: true,
                name: 'heroicons_outline:information-circle',
                color: 'info',
            },
            actions: {
                confirm: {
                    show: true,
                    label: 'ยืนยัน',
                    color: 'primary',
                },
                cancel: {
                    show: true,
                    label: 'ยกเลิก',
                },
            },
            dismissible: true,
        });
        // Subscribe to the confirmation dialog closed action
        confirmation.afterClosed().subscribe((result) => {
            // If the confirm button pressed...
            if (result === 'confirmed') {

                const v = this.formData.getRawValue();

                const payload = {
                    line_id: v.line_id,
                    leave_type_id: v.leave_type_id,
                    type: v.type,
                    date_start: v.date ? v.date : v.date_start,
                    date_end: v.date ? v.date : v.date_end,
                    time_start: v.time_start,        // "HH:mm"
                    time_end: v.time_end,            // "HH:mm"
                    description: v.description?.trim() || null,
                    files: this.files, // แนบไฟล์ตอนเรียก service (FormData)
                };
                this._Service.createLeave(payload).subscribe({
                    next: () => {
                        this._fuseConfirmationService.open({
                            title: 'สำเร็จ',
                            message: 'บันทึกสำเร็จ',
                            icon: {
                                show: true,
                                name: 'heroicons_outline:check-circle',
                                color: 'success',
                            },
                            actions: {
                                confirm: {
                                    show: true,
                                    label: 'ตกลง',
                                    color: 'primary',
                                },
                                cancel: {
                                    show: false,
                                },
                            },
                        }).afterClosed().subscribe(() => {
                            liff.closeWindow();
                        });
                    },
                    error: (err: any) => {
                        this._fuseConfirmationService.open({
                            title: 'เกิดข้อผิดพลาด',
                            message: err.error.message,
                            icon: {
                                show: true,
                                name: 'heroicons_outline:exclamation-triangle',
                                color: 'warning',
                            },
                            actions: {
                                confirm: {
                                    show: true,
                                    label: 'ตกลง',
                                    color: 'primary',
                                },
                                cancel: {
                                    show: false,
                                    label: 'ยกเลิก',
                                },
                            },
                            dismissible: true,
                        });
                    },
                });
            }
        });
    }
    onClose() {
        liff.closeWindow();
    }
    getTypeLeave(lineId: string) {
        this._Service.getTypeLeavenew(lineId).subscribe((res: any) => {
            this.leaveType = res.data;
        });
    }

    onSelect(event) {
        this.files.push(...event.addedFiles);
        // Trigger Image Preview
        setTimeout(() => {
            this._changeDetectorRef.detectChanges();
        }, 150);

        const formData = new FormData();

        // Append the 'path' property to the FormData
        formData.append('path', 'files/leave/');

        // Append the 'image' property to the FormData
        formData.append('file', this.files[0]);

        this._Service.uploadFile(formData).subscribe((res: any) => {
            this.formData.patchValue({
                file: res.data,
            });
        });

    }
    onRemove(event) {
        this.files.splice(this.files.indexOf(event), 1);
        this.formData.patchValue({
            file: '',
        });
    }

    generateTimeOptions() {
        const startHour = 8;
        const endHour = 17;
        const intervalMinutes = 30;

        for (let hour = startHour; hour <= endHour; hour++) {
            for (let min = 0; min < 60; min += intervalMinutes) {
                const formattedHour = hour.toString().padStart(2, '0');
                const formattedMin = min.toString().padStart(2, '0');
                this.timeOptions.push(`${formattedHour}:${formattedMin}`);
            }
        }
    }


    onFilesSelected(evt: Event): void {
        const input = evt.target as HTMLInputElement;
        if (input.files?.length) {
            this.files = [...this.files, ...Array.from(input.files)];
        }
        input.value = '';
    }

    removeFile(i: number): void {
        this.files.splice(i, 1);
        this.files = [...this.files];
    }

    // ===== Validators (Luxon) =====
    private dateRangeValidator = (group: AbstractControl): ValidationErrors | null => {
        const s = group.get('date_start')?.value as DateTime | null;
        const e = group.get('date_end')?.value as DateTime | null;
        if (!s || !e) return null;
        return s.startOf('day') <= e.startOf('day') ? null : { invalidRange: true };
    };

    private timeRangeValidator = (group: AbstractControl): ValidationErrors | null => {
        if (group.get('type')?.value !== 'hour') return null;

        const s = group.get('time_start')?.value as string | null; // "HH:mm"
        const e = group.get('time_end')?.value as string | null;
        const ds = group.get('date_start')?.value as DateTime | null;
        const de = group.get('date_end')?.value as DateTime | null;
        if (!s || !e || !ds || !de) return null;

        // รวมวัน+เวลา เพื่อเทียบช่วงอย่างถูกต้อง (กรณีข้ามวัน)
        const [sh, sm] = s.split(':').map(Number);
        const [eh, em] = e.split(':').map(Number);

        const startDT = ds.set({ hour: sh, minute: sm, second: 0, millisecond: 0 });
        const endDT = de.set({ hour: eh, minute: em, second: 0, millisecond: 0 });

        return startDT < endDT ? null : { invalidTimeRange: true };
    };

    private buildTimeSlots(): string[] {
        const slots: string[] = [];
        const start = DateTime.fromObject({ hour: 8, minute: 0 }, { zone: this.zone });
        const end = DateTime.fromObject({ hour: 18, minute: 0 }, { zone: this.zone });

        let t = start;
        while (t <= end) {
            slots.push(t.toFormat('HH:mm'));
            t = t.plus({ minutes: 30 });
        }
        return slots;
    }

    goback() {
        liff.closeWindow();
    }

}
