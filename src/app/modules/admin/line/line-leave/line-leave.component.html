<div class="flex flex-col flex-auto min-w-0 bg-gray-100 dark:bg-transparent">

    <!-- Header -->
    <mat-toolbar color="primary" class="!h-auto !py-6 !px-10 shadow">
        <div class="flex-1">
            <h2 class="text-3xl md:text-4xl font-extrabold leading-tight m-0">แจ้งลางาน</h2>
        </div>
    </mat-toolbar>

    <div class="flex-auto p-3 sm:p-6 bg-white">
        <mat-card>
            <form [formGroup]="formData" class="grid grid-cols-1 gap-6 p-6 md:p-10">

                <!-- 1) ประเภทการลา -->
                <mat-form-field appearance="outline" class="field">
                    <mat-label>เลือกประเภท</mat-label>
                    <mat-select formControlName="leave_type_id" required>
                        <mat-option *ngFor="let item of leaveType" [value]="item.leave_type_id">
                            {{ item.leave_type?.name }}
                        </mat-option>
                    </mat-select>
                    <mat-error *ngIf="formData.get('leave_type_id')?.hasError('required')">
                        กรุณาเลือกประเภทการลา
                    </mat-error>
                </mat-form-field>

                <!-- 2) ช่วงเวลา (รูปแบบการลา) -->
                <mat-form-field appearance="outline" class="field">
                    <mat-label>เลือกช่วงเวลา</mat-label>
                    <mat-select formControlName="type" required>
                        <mat-option *ngFor="let option of typeOptions" [value]="option.value">
                            {{ option.label }}
                        </mat-option>
                    </mat-select>
                    <mat-error *ngIf="formData.get('type')?.hasError('required')">
                        กรุณาเลือกรูปแบบการลา
                    </mat-error>
                </mat-form-field>

                <!-- 3) วันที่ -->
                <!-- กรณีเต็มวัน: ใช้ Date Range -->
                <mat-form-field appearance="outline" *ngIf="formData.get('type')?.value === 'day'">
                    <mat-label>เลือกช่วงวันที่</mat-label>
                    <mat-date-range-input [rangePicker]="picker">
                        <input matStartDate formControlName="date_start" placeholder="เริ่ม" />
                        <input matEndDate formControlName="date_end" placeholder="สิ้นสุด" />
                    </mat-date-range-input>
                    <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                    <mat-date-range-picker #picker [touchUi]="true"></mat-date-range-picker>
                    <mat-hint>ต้องเลือกทั้งวันเริ่มและวันสิ้นสุด</mat-hint>
                    <mat-error *ngIf="formData.hasError('invalidRange')">ช่วงวันที่ไม่ถูกต้อง</mat-error>
                    <mat-error
                        *ngIf="formData.get('date_start')?.hasError('required') || formData.get('date_end')?.hasError('required')">
                        กรุณาเลือกช่วงวันที่
                    </mat-error>
                </mat-form-field>

                <!-- กรณีครึ่งวัน/รายชั่วโมง: ใช้วันเดียว -->
                <mat-form-field appearance="outline" *ngIf="formData.get('type')?.value !== 'day'">
                    <mat-label>เลือกวันที่</mat-label>
                    <input matInput [matDatepicker]="singlePicker" formControlName="date" placeholder="เลือกวันที่" />
                    <mat-datepicker-toggle matSuffix [for]="singlePicker"></mat-datepicker-toggle>
                    <mat-datepicker #singlePicker [touchUi]="true"></mat-datepicker>
                    <mat-error *ngIf="formData.hasError('singleDateRequired')">กรุณาเลือกวันที่</mat-error>
                </mat-form-field>

                <!-- 4) เวลาเริ่มต้น/เวลาสิ้นสุด (แสดงเฉพาะรายชั่วโมง) -->
                <mat-form-field appearance="outline" *ngIf="formData.get('type')?.value === 'hour'">
                    <mat-label>เวลาเริ่ม</mat-label>
                    <mat-select formControlName="time_start">
                        <mat-option *ngFor="let t of timeOptions" [value]="t">{{ t }}</mat-option>
                    </mat-select>
                    <mat-error *ngIf="formData.get('time_start')?.hasError('required')">ระบุเวลาเริ่ม</mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" *ngIf="formData.get('type')?.value === 'hour'">
                    <mat-label>เวลาสิ้นสุด</mat-label>
                    <mat-select formControlName="time_end">
                        <mat-option *ngFor="let t of timeOptions" [value]="t">{{ t }}</mat-option>
                    </mat-select>
                    <mat-error *ngIf="formData.get('time_end')?.hasError('required')">ระบุเวลาสิ้นสุด</mat-error>
                    <mat-error *ngIf="formData.hasError('invalidTimeRange')">เวลาสิ้นสุดต้องหลังเวลาเริ่ม</mat-error>
                </mat-form-field>

                <!-- 5) รายละเอียดการลา -->
                <mat-form-field appearance="outline">
                    <mat-label>ระบุรายละเอียดการลา</mat-label>
                    <textarea matInput rows="3" formControlName="description"></textarea>
                    <mat-hint align="end">{{ formData.get('description')?.value?.length || 0 }}/500</mat-hint>
                    <mat-error *ngIf="formData.get('description')?.hasError('maxlength')">สูงสุด 500
                        ตัวอักษร</mat-error>
                </mat-form-field>

                <!-- 6) ไฟล์แนบ -->
                <div class="space-y-3">
                    <label class="block font-semibold text-gray-800">ไฟล์แนบ</label>

                    <ngx-dropzone (change)="onSelect($event)" [multiple]="false" [accept]="'.jpg,.jpeg,.png,.pdf'"
                        class="rounded-xl border-2 border-dashed">
                        <ngx-dropzone-label>
                            <div class="text-center p-6">
                                <div class="mb-2 text-sm text-gray-700">ลากไฟล์มาวางที่นี่ หรือคลิกเพื่อเลือกไฟล์ เช่น ใบรับรองแพทย์</div>
                                <div class="text-xs text-gray-500">รองรับ: JPG, PNG, PDF (ไฟล์เดียว)</div>
                            </div>
                        </ngx-dropzone-label>

                        <ngx-dropzone-image-preview *ngFor="let f of files" [file]="f" [removable]="true"
                            (removed)="onRemove(f)" class="m-2">
                            <ngx-dropzone-label>{{ f.name }} ({{ f.type || 'unknown' }})</ngx-dropzone-label>
                        </ngx-dropzone-image-preview>
                    </ngx-dropzone>

                    <div *ngIf="formData.get('file')?.value" class="text-sm text-green-700 bg-green-50 p-3 rounded-lg">
                        อัปโหลดสำเร็จ:
                        <a [href]="formData.get('file')?.value" target="_blank" rel="noopener"
                            class="underline break-all">{{ formData.get('file')?.value }}</a>
                    </div>

                    <div class="pt-1">
                        <button type="button" mat-stroked-button color="warn"
                            (click)="files = []; formData.patchValue({ file: '' })">
                            ล้างไฟล์แนบ
                        </button>
                    </div>
                </div>

                <!-- ปุ่ม -->
                <div class="flex items-center justify-center gap-3 pt-4 border-t">
                    <button mat-stroked-button color="warn" (click)="goback()">
                        <mat-icon svgIcon="heroicons_solid:x-mark" class="mr-2"></mat-icon>
                        ยกเลิก
                    </button>
                    <button mat-stroked-button class="btn-blue" (click)="newLeave()">
                        <mat-icon svgIcon="heroicons_solid:check" class="mr-2"></mat-icon>
                        ยืนยัน
                    </button>
                </div>

            </form>
        </mat-card>
    </div>
</div>