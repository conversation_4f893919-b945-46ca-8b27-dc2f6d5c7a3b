import {
    AfterViewInit,
    ChangeDetectorRef,
    Component,
    On<PERSON><PERSON>roy,
    OnInit,
} from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import {
    MatDialog,
} from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from 'app/core/auth/auth.service';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { LineService } from '../line.service';
import { environment } from 'environments/environment';
import liff from '@line/liff';
import { DateTime } from 'luxon';
import { MatNativeDateModule, MatOptionModule } from '@angular/material/core';
import { MatCardModule } from '@angular/material/card';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatInputModule } from '@angular/material/input';
// import { ImportOSMComponent } from '../card/import-osm/import-osm.component';

@Component({
    selector: 'line-ot',
    templateUrl: './line-ot.component.html',
    styleUrls: ['./line-ot.component.scss'],
    standalone: true,
    animations: fuseAnimations,
    imports: [
        MatNativeDateModule,
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatInputModule,
        MatSelectModule,
        MatOptionModule,
        MatButtonModule,
        MatIconModule,
        MatCardModule,
        MatDatepickerModule,
        MatToolbarModule,
    ],
})
export class LineOtComponent implements OnInit, AfterViewInit, OnDestroy {
    itemtypeData: any;
    statusData = [
        { id: 0, name: 'ปิดการใช้งาน' },
        { id: 1, name: 'เปิดการใช้งาน' },
    ];
    itemtypeId: string;
    formData: FormGroup;
    flashErrorMessage: string;
    flashMessage: 'success' | 'error' | null = null;
    isLoading: boolean = false;
    OtType: any = [];
    statusAC: any = [
        { name: 'approved', value: 'approved' },
        { name: 'cancel', value: 'cancel' },
    ];
    timeOptions: any[] = []
    profile: any;
    liffInitialized = false;
    /**
     * Constructor
     */
    constructor(
        // public dialogRef: MatDialogRef<EditItemTypeComponent>,
        // @Inject(MAT_DIALOG_DATA) private _data,
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseConfirmationService: FuseConfirmationService,
        private _formBuilder: FormBuilder,
        private _Service: LineService,
        private _matDialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _authService: AuthService,
    ) {
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    async ngOnInit(): Promise<void> {


        const today = DateTime.now().toJSDate(); // ค่าเริ่มต้นเป็นวันนี้ (Date)

        this.formData = this._formBuilder.group({
            line_id: '',
            date: [today, Validators.required],       // เก็บเป็น Date เพื่อใช้กับ mat-datepicker
            date_str: [{ value: this.fmt(today), disabled: true }], // แสดงผล/ส่งออกเป็น yyyy-MM-dd
            time_start: ['17:30', Validators.required],
            time_end: ['19:30', Validators.required],
            ot_type_id: ['', Validators.required],
        });
        this.generateTimeOptions();
        // this.getTypeOt('U7f8d3bed4f907ec2b4bd88d5a40d8937');

        try {
            await liff.init({ liffId: environment.LIFF_OT });
            this.liffInitialized = true;

            if (liff.isLoggedIn()) {

                this.profile = await liff.getProfile();

                this.formData.get('line_id')?.setValue(this.profile?.userId);

                this.getTypeOt(this.profile?.userId);
            } else {
                liff.login();
            }
        } catch (error) {
            console.error('LIFF initialization failed', error);
        }
    }

    // ฟังก์ชัน format ด้วย Luxon → yyyy-MM-dd
    private fmt(d: Date): string {
        return DateTime.fromJSDate(d).toFormat('yyyy-LL-dd');
    }

    onDateChange(e: { value: Date | null }) {
        const v = e.value;
        this.formData.patchValue({ date_str: v ? this.fmt(v) : '' }, { emitEvent: false });
    }

    /**
     * After view init
     */
    ngAfterViewInit(): void {

    }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
    }

    goback() {
        liff.closeWindow();
    }

    create(): void {
        this.flashMessage = null;
        this.flashErrorMessage = null;

        const confirmation = this._fuseConfirmationService.open({
            title: 'แจ้งโอที',
            message: 'คุณต้องการแจ้งโอทีใช่หรือไม่ ',
            icon: {
                show: false,
                name: 'heroicons_outline:exclamation-triangle',
                color: 'warning',
            },
            actions: {
                confirm: {
                    show: true,
                    label: 'ยืนยัน',
                    color: 'primary',
                },
                cancel: {
                    show: true,
                    label: 'ยกเลิก',
                },
            },
            dismissible: true,
        });

        // Subscribe to the confirmation dialog closed action
        confirmation.afterClosed().subscribe((result) => {
            const raw = this.formData.getRawValue();
            const payload = {
                line_id: raw.line_id,
                date: raw.date_str,                 // => '2025-08-14' เป็นต้น
                time_start: raw.time_start,
                time_end: raw.time_end,
                ot_type_id: raw.ot_type_id,
            };
            // If the confirm button pressed...
            if (result === 'confirmed') {
                this._Service.createOt(payload).subscribe({
                    next: (resp: any) => {
                        this._fuseConfirmationService.open({
                            title: 'สำเร็จ',
                            message: 'บันทึกสำเร็จ',
                            icon: {
                                show: true,
                                name: 'heroicons_outline:check-circle',
                                color: 'success',
                            },
                            actions: {
                                confirm: {
                                    show: true,
                                    label: 'ตกลง',
                                    color: 'primary',
                                },
                                cancel: {
                                    show: false,
                                },
                            },
                        }).afterClosed().subscribe(() => {
                            liff.closeWindow();
                        });
                    },
                    error: (err: any) => {
                        this._fuseConfirmationService.open({
                            title: 'เกิดข้อผิดพลาด',
                            message: err.error.message,
                            icon: {
                                show: true,
                                name: 'heroicons_outline:exclamation-triangle',
                                color: 'warning',
                            },
                            actions: {
                                confirm: {
                                    show: true,
                                    label: 'ตกลง',
                                    color: 'primary',
                                },
                                cancel: {
                                    show: false,
                                    label: 'ยกเลิก',
                                },
                            },
                            dismissible: true,
                        });
                    },
                });
            }
        });
    }

    showFlashMessage(type: 'success' | 'error'): void {
        // Show the message
        this.flashMessage = type;

        // Mark for check
        this._changeDetectorRef.markForCheck();

        // Hide it after 3 seconds
        setTimeout(() => {
            this.flashMessage = null;

            // Mark for check
            this._changeDetectorRef.markForCheck();
        }, 3000);
    }

    getTypeOt(lineId: string) {
        this._Service.getTypeOt(lineId).subscribe((res: any) => {
            this.OtType = res.data;
        });
    }

    generateTimeOptions() {
        const startHour = 0; // เริ่มจาก 00:00
        const endHour = 23;  // จบที่ 23:30
        const interval = 30; // นาที

        for (let hour = startHour; hour <= endHour; hour++) {
            for (let minute = 0; minute < 60; minute += interval) {
                const h = hour.toString().padStart(2, '0');
                const m = minute.toString().padStart(2, '0');
                this.timeOptions.push(`${h}:${m}`);
            }
        }
    }
}
