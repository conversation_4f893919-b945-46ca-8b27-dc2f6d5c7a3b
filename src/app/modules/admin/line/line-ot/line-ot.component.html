<div class="flex flex-col flex-auto min-w-0 bg-gray-100 dark:bg-transparent">

    <!-- Header -->
    <mat-toolbar color="primary" class="!h-auto !py-6 !px-10 shadow">
        <div class="flex-1">
            <h2 class="text-3xl md:text-4xl font-extrabold leading-tight m-0">
                แจ้งโอที
            </h2>
        </div>
    </mat-toolbar>

    <div class="flex-auto p-3 sm:p-6 bg-white">
        <mat-card>
            <form [formGroup]="formData" class="grid grid-cols-1 gap-6 p-6 md:p-10">

                <!-- วันที่ -->
                <mat-form-field appearance="outline">
                    <mat-label>วันที่</mat-label>
                    <input matInput [matDatepicker]="picker" formControlName="date" (dateChange)="onDateChange($event)"
                        placeholder="เลือกวันที่" />
                    <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                    <mat-datepicker #picker></mat-datepicker>
                    <!-- <mat-hint align="start">จะถูกส่งออกเป็นรูปแบบ yyyy-MM-dd</mat-hint> -->
                </mat-form-field>

                <!-- เวลาเริ่ม -->
                <mat-form-field appearance="outline">
                    <mat-label>เวลาเริ่ม</mat-label>
                    <mat-select formControlName="time_start">
                        <mat-option *ngFor="let time of timeOptions" [value]="time">
                            {{ time }}
                        </mat-option>
                    </mat-select>
                </mat-form-field>

                <!-- เวลาสิ้นสุด -->
                <mat-form-field appearance="outline">
                    <mat-label>เวลาสิ้นสุด</mat-label>
                    <mat-select formControlName="time_end">
                        <mat-option *ngFor="let time of timeOptions" [value]="time">
                            {{ time }}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
                <!-- ประเภท -->
                <mat-form-field appearance="outline">
                    <mat-label>ประเภท</mat-label>
                    <mat-select formControlName="ot_type_id">
                        <mat-option *ngFor="let item of OtType" [value]="item.id">
                            {{ item.name }}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
                <!-- ปุ่ม -->
                <div class="flex items-center justify-center gap-3 pt-4 border-t">
                    <button mat-stroked-button color="warn" (click)="goback()">
                        <mat-icon svgIcon="heroicons_solid:x-mark" class="mr-2"></mat-icon>
                        ยกเลิก
                    </button>
                    <button mat-stroked-button class="btn-blue" (click)="create()">
                        <mat-icon svgIcon="heroicons_solid:check" class="mr-2"></mat-icon>
                        ยืนยัน
                    </button>
                </div>

            </form>
        </mat-card>
    </div>
</div>