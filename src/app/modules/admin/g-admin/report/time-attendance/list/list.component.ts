import {
    AfterViewInit,
    ChangeDetector<PERSON>ef,
    Component,
    ElementRef,
    On<PERSON><PERSON>roy,
    OnInit,
    ViewChild,
} from '@angular/core';
import { FormGroup, FormBuilder, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MAT_DATE_FORMATS, MatNativeDateModule } from '@angular/material/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { DataTableDirective } from 'angular-datatables';
import { Subject, lastValueFrom } from 'rxjs';
import { ItemTypeService } from '../../../item-type/item-type.service';
import { TimeAttendanceService } from '../time-attendance.service';
import { DatePip<PERSON>, NgIf, NgFor, CommonModule } from '@angular/common';
import { environment } from 'environments/environment';
import Swal from 'sweetalert2';
import moment from 'moment';
import { PersonnelAutocompleteComponent } from 'app/shared/personnel-autocomplete/personnel-autocomplete.component';
import { MatProgressBar } from '@angular/material/progress-bar';
import { MatAnchor, MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { PersonnelAutocompleteComponent as PersonnelAutocompleteComponent_1 } from '../../../../../../shared/personnel-autocomplete/personnel-autocomplete.component';
import { MatFormField, MatLabel, MatSuffix } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { MatDatepickerInput, MatDatepickerToggle, MatDatepicker } from '@angular/material/datepicker';

import * as XLSX from 'xlsx';
import { BuddhistDatePipe } from 'app/modules/admin/pipes/buddhist-date.pipe';

@Component({
    selector: 'app-list',
    templateUrl: './list.component.html',
    styleUrls: ['./list.component.scss'],
    animations: fuseAnimations,
    imports: [
        CommonModule,
        NgIf,
        MatProgressBar,
        MatAnchor,
        MatIcon,
        FormsModule,
        ReactiveFormsModule,
        MatButton,
        PersonnelAutocompleteComponent_1,
        MatFormField,
        MatLabel,
        MatInput,
        MatDatepickerInput,
        MatDatepickerToggle,
        MatSuffix,
        MatDatepicker,
        MatNativeDateModule,
        NgFor,
        DatePipe,
        BuddhistDatePipe
    ]
})
export class ListComponent implements OnInit, AfterViewInit, OnDestroy {
    @ViewChild('printArea', { static: false }) printArea!: ElementRef;
    @ViewChild('personnelAutocomplete', { static: false }) personnelAutocomplete!: PersonnelAutocompleteComponent;
    @ViewChild(DataTableDirective)
    dtElement!: DataTableDirective;
    public dtOptions: DataTables.Settings = {};
    formFieldHelpers: string[] = ['fuse-mat-dense'];
    public dataRow: any[] = [];
    private destroy$ = new Subject<any>();
    totalRowSummary: any;
    flashMessage: 'success' | 'error' | null = null;
    isLoading: boolean = false;
    selectedLocation = '';
    selectedItemType = '';
    selectedItem = '';
    itemtypeData: any[] = [];
    itemData: any = [];
    now = new Date();
    form: FormGroup;

    /**
     * Constructor
     */
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseConfirmationService: FuseConfirmationService,
        private _formBuilder: FormBuilder,
        private _Service: TimeAttendanceService,
        private _ServiceItemtemType: ItemTypeService,
        private _matDialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _dialog: MatDialog,
    ) {
        this.form = this._formBuilder.group({
            personnel_id: ['', Validators.required],
            date_start: [new Date(), Validators.required],
            date_end: [new Date(), Validators.required],
        });
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    async ngOnInit(): Promise<void> {
        const itemtype = await lastValueFrom(this._Service.getUser(''));
        this.itemtypeData = itemtype.data;
    }
    onPersonnelSelected(selectedPersonnel: any): void {
        this.form.patchValue({
            personnel_id: selectedPersonnel.personnel_id,
        })
    }
    hiddenEdit() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 27);
        return menu.edit == 0;
    }
    hiddenDelete() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 27);
        return menu.delete == 0;
    }
    hiddenSave() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 27);
        return menu.save == 0;
    }
    /**
     * After view init
     */
    ngAfterViewInit(): void { }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        // this._unsubscribeAll.next(null);
        // this._unsubscribeAll.complete();
    }

    onChangeItemType(e): void {
        // this._Service.getByItemType(e).subscribe((resp: any) => {
        //     this.itemData = resp.data;
        //     // this.rawDataFilter = this.dataRow
        // });
    }

    showFlashMessage(type: 'success' | 'error'): void {
        // Show the message
        this.flashMessage = type;
        // Mark for check
        this._changeDetectorRef.markForCheck();

        // Hide it after 3 seconds
        setTimeout(() => {
            this.flashMessage = null;

            // Mark for check
            this._changeDetectorRef.markForCheck();
        }, 3000);
    }

    pages = { current_page: 1, last_page: 1, per_page: 10, begin: 0 };

    rerender(): void {
        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
            dtInstance.ajax.reload();
        });
    }

    print(): void {
        const el = document.getElementById('print-section');
        if (!el) return;

        const printContents = el.innerHTML;
        const popupWin = window.open('', '_blank', 'top=0,left=0,height=100%,width=auto');
        popupWin!.document.open();
        popupWin!.document.write(`
  <html>
    <head>
      <title>รายงานสินค้าตามประเภทสินค้า</title>
      <meta charset="utf-8" />
      <style>
        /* ---------- พื้นฐานและโทนสีสำหรับงานพิมพ์ ---------- */
        :root{
          --ink: #111827;        /* สีตัวอักษรหลัก (เทาเข้ม) */
          --muted: #4b5563;      /* สีตัวอักษรรอง */
          --line: #d1d5db;       /* สีเส้นตาราง */
          --thead-bg: #f3f4f6;   /* พื้นหลังหัวตาราง */
          --row-alt: #fafafa;    /* พื้นหลังแถวสลับ */
          --accent: #d4a85d;     /* เส้นเน้นหัวรายงาน (ทองหม่น) */
        }

        html, body {
          margin: 0;
          padding: 0;
          color: var(--ink);
          font: 12px/1.6 -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans Thai", "Sarabun", sans-serif;
          -webkit-print-color-adjust: exact !important;
          print-color-adjust: exact !important;
        }

        .page-body {
          width: 100%;
          margin: 0 auto;
          padding: 16px 20px 12px 20px;
          background: white;
        }

        /* ---------- ส่วนหัวรายงาน (ให้สวยขึ้นตอนพิมพ์) ---------- */
        .report-header{
          display: flex;
          justify-content: space-between;
          align-items: flex-end;
          margin-bottom: 12px;
          padding-bottom: 10px;
          border-bottom: 2px solid var(--accent);
        }
        .report-title{
          font-size: 18px;
          font-weight: 700;
          letter-spacing: .2px;
        }
        .report-meta{
          text-align: right;
          font-size: 11px;
          color: var(--muted);
        }

        /* ---------- ตารางแบบพิมพ์สวย ---------- */
        table{
          width: 100%;
          border-collapse: collapse;
        }
        thead th{
          background: var(--thead-bg);
          color: var(--ink);
          font-weight: 600;
          text-align: left;
          border: 1px solid var(--line);
          padding: 6px 8px;
          font-size: 12px;
        }
        tbody td{
          border: 1px solid var(--line);
          padding: 6px 8px;
          font-size: 11.5px;
          vertical-align: top;
        }
        tbody tr:nth-child(odd){
          background: var(--row-alt);
        }

        /* ป้องกันการตัดหน้าในแถวสำคัญ */
        .avoid-break, table tr{
          page-break-inside: avoid;
          break-inside: avoid;
        }

        /* ป้าย/แบดจ์/ชิปให้เป็นโทนเส้นกรอบบาง ๆ เพื่อพิมพ์ */
        .badge, .chip{
          border: 1px solid var(--line);
          padding: 2px 6px;
          border-radius: 999px;
          font-size: 10.5px;
          color: var(--ink);
          background: transparent;
        }

        /* ลิงก์ให้ดูเหมือนข้อความทั่วไปตอนพิมพ์ */
        a{
          color: inherit;
          text-decoration: none;
        }

        /* ซ่อนสิ่งที่ไม่ต้องการพิมพ์ */
        .no-print{ display: none !important; }

        /* ---------- กฎตอนพิมพ์ ---------- */
@media print {
  @page { size: A4 portrait; margin: 10mm; }

  #print-section table {
    font-size: 11px;
    border-collapse: collapse;
  }
  #print-section th, 
  #print-section td {
    border: 1px solid #d1d5db;
    padding: 4px 6px !important;
  }
  #print-section tbody tr:nth-child(odd) {
    background: #fafafa;
  }

  /* ให้สีไม่หายตอนพิมพ์ */
  html, body {
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
}
      </style>
    </head>
    <body onload="window.print();window.close()">
      <div class="page-body">
        <!-- ถ้าภายใน #print-section ไม่มีหัวรายงาน สามารถใช้บล็อกนี้ได้
        <div class="report-header">
          <div class="report-title">รายงานสินค้าตามประเภทสินค้า</div>
          <div class="report-meta">
            พิมพ์เมื่อ: ${new Date().toLocaleString('th-TH')}
          </div>
        </div>
        -->
        ${printContents}
      </div>
    </body>
  </html>`);
        popupWin!.document.close();
    }


    onFilter() {
        this.rerender();
    }

    printExcel() {
        if (this.form.invalid) {
            let errorMessage = '';

            if (this.form.controls['date_start'].invalid) {
                errorMessage += 'กรุณาเลือกวันที่เริ่มต้น\n';
            }
            if (this.form.controls['date_end'].invalid) {
                errorMessage += 'กรุณาเลือกวันที่สิ้นสุด\n';
            }
            this.showError(errorMessage.trim());
            return;
        }
        const datePipe = new DatePipe("en-US");

        const start_date = datePipe.transform(
            this.form.value.date_start,
            "YYYY-MM-dd"
        );
        const end_date = datePipe.transform(
            this.form.value.date_end,
            "YYYY-MM-dd"
        );

        this.form.patchValue({
            date_start: start_date,
            date_end: end_date,
        });

        window.open(environment.API_URL + `api/export_time_in_out?personnel_id=${this.form.value.personnel_id}&date_start=${this.form.value.date_start}&date_end=${this.form.value.date_end}`

        )
    }

    totalPriceTable() {
        let total = 0;
        for (let data of this.dataRow) {
            total += Number(data.price);
        }
        return total;
    }

    genPDF() {
        window.open(
            'https://wmk1.net/api/public/api/genPDF?bank_id=' +
            this.form.value.bank_id +
            '&text=' +
            this.form.value.text +
            '&start_date=' +
            this.form.value.start_date +
            '&end_date=' +
            this.form.value.end_date +
            '&type=' +
            this.form.value.type
        );
    }

    GetReport(): void {
        const datePipe = new DatePipe("en-US");

        const start_date = datePipe.transform(
            this.form.value.date_start,
            "YYYY-MM-dd"
        );
        const end_date = datePipe.transform(
            this.form.value.date_end,
            "YYYY-MM-dd"
        );

        this.form.patchValue({
            date_start: start_date,
            date_end: end_date,
        });

        const formValue = this.form.value;
        this._Service.getReport(formValue).subscribe({
            next: (resp: any) => {
                const rows = Array.isArray(resp?.data) ? resp.data : [];
                this.dataRow = [...rows].sort(
                    (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime() // เปลี่ยน 'date' เป็น key ของคุณ
                );
                this._changeDetectorRef.markForCheck();
            },
            error: (err: any) => {

                this._fuseConfirmationService.open({
                    "title": "เกิดข้อผิดพลาด",
                    "message": err.error.message,
                    "icon": {
                        "show": true,
                        "name": "heroicons_outline:exclamation-triangle",
                        "color": "warning"
                    },
                    "actions": {
                        "confirm": {
                            "show": false,
                            "label": "ยืนยัน",
                            "color": "primary"
                        },
                        "cancel": {
                            "show": false,
                            "label": "ยกเลิก",

                        }
                    },
                    "dismissible": true
                });
            }
        });
    }

    SyncTime(): void {
        if (this.form.invalid) {
            let errorMessage = '';

            if (this.form.controls['date_start'].invalid) {
                errorMessage += 'กรุณาเลือกวันที่เริ่มต้น\n';
            }
            if (this.form.controls['date_end'].invalid) {
                errorMessage += 'กรุณาเลือกวันที่สิ้นสุด\n';
            }

            this.showError(errorMessage.trim());
            return;
        }
        const datePipe = new DatePipe("en-US");

        const start_date = datePipe.transform(
            this.form.value.date_start,
            "YYYY-MM-dd"
        );
        const end_date = datePipe.transform(
            this.form.value.date_end,
            "YYYY-MM-dd"
        );

        this.form.patchValue({
            date_start: start_date,
            date_end: end_date,
        });

        const formValue = this.form.value;

        let timerInterval
        Swal.fire({
            allowOutsideClick: false,
            icon: 'info',
            title: 'กำลังดึงข้อมูลเวลา...',
            timerProgressBar: true,
            didOpen: () => {
                Swal.showLoading()
                const b: any = Swal.getHtmlContainer().querySelector('b')

            },
            willClose: () => {
                clearInterval(timerInterval)
            }
        })
        this._Service.syncTime(formValue).subscribe((res) => {
            if (res) {
                Swal.close();
                this._changeDetectorRef.markForCheck();
            }
        },
            (err) => {
                Swal.close();
                Swal.fire({
                    icon: 'error',
                    title: 'Please contact Admin !!',
                })
            })
    }

    UpdateAttendance(): void {
        if (this.form.invalid) {
            let errorMessage = '';

            if (this.form.controls['date_start'].invalid) {
                errorMessage += 'กรุณาเลือกวันที่เริ่มต้น\n';
            }
            if (this.form.controls['date_end'].invalid) {
                errorMessage += 'กรุณาเลือกวันที่สิ้นสุด\n';
            }

            this.showError(errorMessage.trim());
            return;
        }
        const datePipe = new DatePipe("en-US");

        const start_date = datePipe.transform(
            this.form.value.date_start,
            "YYYY-MM-dd"
        );
        const end_date = datePipe.transform(
            this.form.value.date_end,
            "YYYY-MM-dd"
        );
        this.form.patchValue({
            date_start: start_date,
            date_end: end_date,
        });
        const formValue = this.form.value;

        let timerInterval
        Swal.fire({
            allowOutsideClick: false,
            icon: 'info',
            title: 'กำลัง Update Attendance...',
            timerProgressBar: true,
            didOpen: () => {
                Swal.showLoading()
                const b: any = Swal.getHtmlContainer().querySelector('b')
            },
            willClose: () => {
                clearInterval(timerInterval)
            }
        })
        this._Service.updateAttendance(formValue).subscribe((res) => {
            if (res) {
                Swal.close();
                this._changeDetectorRef.markForCheck();
            }
        },
            (err) => {
                Swal.close();
                Swal.fire({
                    icon: 'error',
                    title: 'Please contact Admin !!',
                })
            })
    }

    exportReport() {
        // if (this.form.invalid) {
        //     let errorMessage = '';

        //     if (this.form.controls['date_start'].invalid) {
        //         errorMessage += 'กรุณาเลือกวันที่เริ่มต้น\n';
        //     }
        //     if (this.form.controls['date_end'].invalid) {
        //         errorMessage += 'กรุณาเลือกวันที่สิ้นสุด\n';
        //     }
        //     this.showError(errorMessage.trim());
        //     return;
        // }

        const date_start = moment(this.form.value.date_start).format('YYYY-MM-DD');
        const date_end = moment(this.form.value.date_end).format('YYYY-MM-DD');
        window.open(`${environment.API_URL}api/report_IndividualReport?personnel_id=${this.form.value.personnel_id}&date_start=${date_start}&date_end=${date_end}`)
    }
    checkFormAndProceed(action: () => void): void {
        if (this.form.invalid) {
            let errorMessage = '';

            if (this.form.controls['date_start'].invalid) {
                errorMessage += 'กรุณาเลือกวันที่เริ่มต้น\n';
            }
            if (this.form.controls['date_end'].invalid) {
                errorMessage += 'กรุณาเลือกวันที่สิ้นสุด\n';
            }

            this.showError(errorMessage.trim());
        }
    }

    showError(message: string): void {
        this._fuseConfirmationService.open({
            "title": "เกิดข้อผิดพลาด",
            "message": message,
            "icon": {
                "show": true,
                "name": "heroicons_outline:exclamation-triangle",
                "color": "warning"
            },
            "actions": {
                "confirm": {
                    "show": false,
                    "label": "ยืนยัน",
                    "color": "primary"
                },
                "cancel": {
                    "show": false,
                    "label": "ยกเลิก",

                }
            },
            "dismissible": true
        });
    }

    exportExcel() {
        // แปลงข้อมูลให้อ่านง่ายในไฟล์
        const rows = (this.dataRow || []).map((it, i) => ({
            ลำดับ: i + 1,
            ชื่อพนักงาน: `${it?.user?.first_name ?? ''} ${it?.user?.last_name ?? ''}`.trim(),
            ตำแหน่ง: it?.user?.position?.name ?? '-',
            สถานะ: this.attendanceLabel(it?.attendance),
            วันที่: this.safe(it?.date, 'date'),
            เวลาเข้างาน: it?.time_in ?? '-',
            เวลาออกงาน: it?.time_out ?? '-',
            เวลาเริ่มพัก: it?.time_brake_in ?? '-',
            เวลาเลิกพัก: it?.time_brake_out ?? '-',
        }));

        // สร้างชีท
        const ws = XLSX.utils.json_to_sheet(rows, {
            header: [
                'ลำดับ', 'ชื่อพนักงาน', 'ตำแหน่ง', 'สถานะ', 'วันที่',
                'เวลาเข้างาน', 'เวลาออกงาน', 'เวลาเริ่มพัก', 'เวลาเลิกพัก'
            ]
        });

        // กำหนดความกว้างคอลัมน์ (ปรับได้)
        ws['!cols'] = [
            { wch: 6 }, { wch: 24 }, { wch: 22 }, { wch: 10 }, { wch: 12 },
            { wch: 12 }, { wch: 12 }, { wch: 12 }, { wch: 12 },
        ];

        // แทรกหัวรายงานบนสุด (สองบรรทัด)
        const title = [['รายงานเวลาเข้างาน'], [`วันที่พิมพ์: ${this.formatNow()}`], []];
        const titleSheet = XLSX.utils.aoa_to_sheet(title);
        XLSX.utils.sheet_add_json(titleSheet, rows, { origin: -1, skipHeader: false });

        // รวมเซลล์หัวเรื่องให้กินทั้ง 9 คอลัมน์
        titleSheet['!merges'] = [
            { s: { r: 0, c: 0 }, e: { r: 0, c: 8 } }, // แถวที่ 1
            { s: { r: 1, c: 0 }, e: { r: 1, c: 8 } }, // แถวที่ 2
        ];

        // ใส่สไตล์ง่าย ๆ (Excel จะเก็บเป็นค่า ไม่ใช่สไตล์ advance)
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, titleSheet, 'รายงาน');

        XLSX.writeFile(wb, `รายงานเวลาเข้างาน_${this.timestamp()}.xlsx`);
    }

    attendanceLabel(code: string) {
        switch (code) {
            case 'normal': return 'ปกติ';
            case 'late': return 'สาย';
            case 'miss': return 'ขาด';
            case 'leave': return 'ลา';
            case 'leave_hour': return 'ลาชั่วโมง';
            case 'off': return 'หยุด';
            default: return '-';
        }
    }

    safe(date: any, type: 'date' | 'time' = 'date') {
        try {
            const d = new Date(date);
            if (isNaN(+d)) return '-';
            return type === 'date'
                ? d.toLocaleDateString('th-TH', { day: '2-digit', month: '2-digit', year: 'numeric' })
                : d.toLocaleTimeString('th-TH', { hour: '2-digit', minute: '2-digit' });
        } catch { return '-'; }
    }

    formatNow() {
        const d = new Date();
        return d.toLocaleString('th-TH', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' });
    }

    timestamp() {
        const d = new Date();
        const pad = (n: number) => String(n).padStart(2, '0');
        return `${d.getFullYear()}${pad(d.getMonth() + 1)}${pad(d.getDate())}_${pad(d.getHours())}${pad(d.getMinutes())}`;
    }

    printPDF() {
        // ใช้ความสามารถ Print ของเบราว์เซอร์ แล้ว Save as PDF
        // เราเตรียมหัวกระดาษด้วย .print:block แล้ว
        window.print();
    }

    printReport(): void {
        if (!this.printArea) return;
        const title = 'รายงานเวลาเข้างาน';
        const subtitle = this.formatDateTime(this.now); // <-- แปลงเป็นข้อความ

        const printStyles = `
    <style>
      @page { size: A4 landscape; margin: 12mm; }
      * { -webkit-print-color-adjust: exact; print-color-adjust: exact; }
      body { font-family: "Sarabun","TH Sarabun New",Tahoma,Arial,sans-serif; font-size: 11pt; color:#111; }
      h1 { font-size: 16pt; margin: 0 0 4mm 0; }
      .subtitle { font-size: 10pt; color:#555; margin-bottom: 6mm; }

      /* ตาราง */
      table { width: 100%; border-collapse: collapse; table-layout: fixed; }
      thead { display: table-header-group; }   /* ซ้ำหัวตารางทุกหน้า */
      tfoot { display: table-footer-group; }
      th, td { border: 1px solid #ddd; padding: 4px 6px; vertical-align: middle; }
      th { background: #f2f5f8; font-weight: 600; text-align: left; }
      th:nth-child(1), td:nth-child(1) { width: 24px; text-align: center; }
      th:nth-child(4), td:nth-child(4) { width: 60px; text-align: center; }
      th:nth-child(5), td:nth-child(5) { width: 80px; text-align: center; }
      th:nth-child(6), td:nth-child(6),
      th:nth-child(7), td:nth-child(7),
      th:nth-child(8), td:nth-child(8),
      th:nth-child(9), td:nth-child(9) { width: 90px; text-align: center; }
      tbody tr:nth-child(odd) td { background: #fafafa; }
      tr { page-break-inside: avoid; }

      /* แก้ overflow ของกล่องที่หุ้มตารางเวลาไปพิมพ์ */
      #print-section { overflow: visible !important; }

      /* เฮดเดอร์/ฟุตเตอร์ (มีผลในบางเบราว์เซอร์เท่านั้น) */
      .page-header { position: running(pageHeader); }
      .page-footer { position: running(pageFooter); }
      @page { 
        @top-center { content: element(pageHeader); }
        @bottom-right { content: element(pageFooter); }
      }
      .ph-wrap { font-size: 10pt; color:#444; display:flex; justify-content:space-between; }
      .pf-wrap { font-size: 10pt; color:#666; }
      .page-number:after { content: counter(page); }
    </style>
  `;

        const headerHtml = `
    <div class="page-header">
      <div class="ph-wrap">
        <div><strong>${title}</strong></div>
        <div>${subtitle}</div>
      </div>
    </div>
  `;
        const footerHtml = `
    <div class="page-footer">
      <div class="pf-wrap">หน้า <span class="page-number"></span></div>
    </div>
  `;

        const content = this.printArea.nativeElement.innerHTML;

        const win = window.open('', '_blank', 'noopener,noreferrer,width=1200,height=800');
        if (!win) return;

        win.document.open();
        win.document.write(`<!doctype html>
    <html>
      <head>
        <meta charset="utf-8">
        <title>${title}</title>
        ${printStyles}
      </head>
      <body>
        ${headerHtml}
        <h1 style="margin-top:0">${title}</h1>
        <div class="subtitle">วันที่พิมพ์: ${subtitle}</div>
        ${content}
        ${footerHtml}
        <script>
          setTimeout(function(){ window.focus(); window.print(); window.close(); }, 200);
        <\/script>
      </body>
    </html>`);
        win.document.close();
    }

    /** ช่วยจัดรูปแบบวันที่/เวลาเป็น dd/MM/yyyy HH:mm */
    private formatDateTime(d: Date | string): string {
        const date = d instanceof Date ? d : new Date(d);
        const dd = String(date.getDate()).padStart(2, '0');
        const mm = String(date.getMonth() + 1).padStart(2, '0');
        const yyyy = date.getFullYear();
        const HH = String(date.getHours()).padStart(2, '0');
        const MM = String(date.getMinutes()).padStart(2, '0');
        return `${dd}/${mm}/${yyyy} ${HH}:${MM}`;
    }

    onPrint(): void {
        // แค่สั่งพิมพ์ทั้งหน้า แต่ CSS จะซ่อนทุกส่วนที่ไม่ใช่ #printArea ให้เอง
        window.print();
    }



}
