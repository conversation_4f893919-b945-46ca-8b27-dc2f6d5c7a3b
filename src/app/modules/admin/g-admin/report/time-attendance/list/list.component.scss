/* ซ่อนปุ่ม/ส่วนที่ไม่ต้องพิมพ์ */ 
.no-print { @media print { display: none !important; } }

@media print {
    @page {
        size: A4 landscape;
        margin: 12mm;
    }

    /* ฆ่า constraint ทั้งหมดจาก ancestor ที่ทำให้เกิดสกรอล */
    html,
    body {
        height: auto !important;
        overflow: visible !important;
    }

    *,
    *::before,
    *::after {
        max-height: none !important;
        height: auto !important;
        /* ปลอดภัยในโหมดพิมพ์ */
        overflow: visible !important;
        /* ตัดสกรอลทิ้ง */
        transform: none !important;
        /* กัน Chrome ไม่แตกหน้า */
        position: static !important;
        /* กัน sticky/fixed */
    }

    /* ซ่อน overlay/panel ของ Material/CDK */
    .cdk-overlay-container,
    .mat-datepicker-content,
    .mat-select-panel,
    .mat-autocomplete-panel,
    .mat-mdc-menu-surface,
    .mat-mdc-tooltip {
        display: none !important;
        visibility: hidden !important;
    }

    /* แสดงเฉพาะพื้นที่พิมพ์ (ใช้ display แทน visibility เพื่อลด side-effect) */
    body> :not(#print-portal):not(#printArea) {
        display: none !important;
    }

    #printArea {
        display: block !important;
    }

    /* ตาราง + หัวซ้ำ + แบ่งหน้า */
    table {
        width: 100%;
        border-collapse: collapse;
        table-layout: fixed;
        page-break-after: auto;
    }

    thead {
        display: table-header-group !important;
        background: #f8fafc;
    }

    tfoot {
        display: table-footer-group !important;
    }

    tbody {
        break-inside: auto !important;
        page-break-inside: auto !important;
    }

    tr {
        break-inside: avoid-page !important;
        page-break-inside: avoid !important;
    }

    th,
    td {
        border: 1px solid #ddd;
        padding: 4px 6px;
    }

    * {
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
}