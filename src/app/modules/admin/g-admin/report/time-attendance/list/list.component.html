<div class="sm:absolute sm:inset-0 flex flex-col flex-auto min-w-0 sm:overflow-auto bg-card dark:bg-transparent">
    <!-- Header -->
    <div
        class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-4 md:px-4 border-b no-print">
        <!-- Loader -->
        <div class="absolute inset-x-0 bottom-0" *ngIf="isLoading">
            <mat-progress-bar [mode]="'indeterminate'"></mat-progress-bar>
        </div>
        <!-- Title -->
        <div class="text-xl md:text-4xl font-extrabold tracking-tight mb-2">รายงานเวลาเข้างาน</div>
        <!-- Actions -->
        <div class="flex flex-row justify-center md:justify-end items-center gap-2">
            <a mat-flat-button color="primary" class="!bg-[#1976d2] text-white hover:!bg-[#1565c0]"
                (click)="onPrint()" *ngIf="!hiddenSave()">
                <mat-icon [svgIcon]="'heroicons_outline:document'"></mat-icon>
                <span class="ml-2 mr-1">พิมพ์รายงาน</span>
            </a>

            <a mat-flat-button class="!bg-[#28a745] text-white hover:!bg-[#218838]" (click)="exportExcel()"
                *ngIf="!hiddenSave()">
                <span class="mdi--file-excel"></span>
                <span class="ml-2 mr-1">Excel</span>
            </a>

            <a mat-flat-button class="!bg-[#0288d1] text-white hover:!bg-[#0277bd]" (click)="SyncTime()"
                *ngIf="!hiddenSave()">
                <mat-icon svgIcon="sync"></mat-icon>
                <span class="ml-2 mr-1">Sync Time</span>
            </a>

            <a mat-flat-button class="!bg-[#7b1fa2] text-white hover:!bg-[#6a1b9a]" (click)="UpdateAttendance()"
                *ngIf="!hiddenSave()">
                <mat-icon svgIcon="sync"></mat-icon>
                <span class="ml-2 mr-1">Update Attendance</span>
            </a>
        </div>

    </div>
    <div class="flex-auto">
        <form class="flex flex-col pt-4 pb-4 bg-card shadow overflow-hidden ng-valid no-print" [formGroup]="form">
            <div class="flex flex-col flex-auto min-w-0 bg-gray-100 dark:bg-transparent ">
                <div class="flex flex-col p-3 sm:p-2 bg-card">
                    <div class="flex mt-0 sm:mt-6 md:mt-6 justify-end px-4 gap-2">
                        <button mat-flat-button
                            class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
                            (click)="GetReport()">
                            <mat-icon [svgIcon]="'heroicons_outline:magnifying-glass'"></mat-icon>
                            <span class="ml-2 mr-1">ค้นหา</span>
                        </button>
                        <button mat-flat-button
                            class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
                            type="reset">
                            <mat-icon [svgIcon]="'heroicons_outline:arrow-path'"></mat-icon>
                            <span class="ml-2 mr-1">ล้าง</span>
                        </button>

                    </div>
                    <div class="border-b flex flex-col md:flex-row gap-4 w-full p-4">
                        <div class="flex flex-col w-full">
                            <ng-container *ngIf="itemtypeData.length > 0">
                                <mat-label class="font-semibold mb-[3px]">เลือกพนักงาน*</mat-label>
                                <app-personnel-autocomplete [itemtypeData]="itemtypeData"
                                    (personnelSelected)="onPersonnelSelected($event)">
                                </app-personnel-autocomplete>
                            </ng-container>
                        </div>
                        <div class="flex flex-col w-full">
                            <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
                                <mat-label>วันเริ่มต้น</mat-label>
                                <input readonly [formControlName]="'date_start'" matInput placeholder="วันที่เริ่มต้น"
                                    [matDatepicker]="picker_start_date">
                                <mat-datepicker-toggle matSuffix [for]="picker_start_date"></mat-datepicker-toggle>
                                <mat-datepicker #picker_start_date></mat-datepicker>
                            </mat-form-field>
                        </div>
                        <div class="flex flex-col w-full">
                            <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
                                <mat-label>วันสิ้นสุด</mat-label>
                                <input readonly [formControlName]="'date_end'" matInput placeholder="วันที่สิ้นสุด"
                                    [matDatepicker]="picker_end_date">
                                <mat-datepicker-toggle matSuffix [for]="picker_end_date"></mat-datepicker-toggle>
                                <mat-datepicker #picker_end_date></mat-datepicker>
                            </mat-form-field>
                        </div>
                    </div>

                </div>
            </div>


        </form>
        <div class="bg-white shadow-sm overflow-hidden print:shadow-none px-4">
            <div class="overflow-x-auto print:overflow-visible" id="printArea"  #printArea>
                <!-- หัวรายงาน (โชว์เฉพาะตอนพิมพ์) -->
                <div class="hidden print:block text-center mb-4">
                    <div class="text-xl font-bold">รายงานเวลาเข้างาน</div>
                    <div class="text-sm">วันที่พิมพ์: {{ now | date:'dd/MM/yyyy HH:mm' }}</div>
                </div>

                <table class="w-full text-slate-700 table-fixed border-collapse">
                    <!-- คุมสัดส่วนคอลัมน์ -->
                    <colgroup>
                        <col style="width:5%"> <!-- ลำดับ -->
                        <col style="width:10%"> <!-- ชื่อพนักงาน -->
                        <col style="width:20%"> <!-- ชื่อพนักงาน -->
                        <col style="width:16%"> <!-- ตำแหน่ง -->
                        <col style="width:8%"> <!-- สถานะ -->
                        <col style="width:10%"> <!-- วันที่ -->
                        <col style="width:10%"> <!-- เวลาเข้างาน -->
                        <col style="width:10%"> <!-- เวลาออกงาน -->
                        <col style="width:10%"> <!-- เริ่มพัก -->
                        <col style="width:11%"> <!-- เลิกพัก -->
                    </colgroup>
                    <!-- ส่วนหัวตาราง -->
                    <thead class="bg-slate-50 print:bg-white border">
                        <tr class="text-slate-800 font-semibold">
                            <th class="py-2 px-2 text-center">ลำดับ</th>
                            <th class="py-2 px-2 text-left">รหัสพนักงาน</th>
                            <th class="py-2 px-2 text-left">ชื่อพนักงาน</th>
                            <th class="py-2 px-2 text-left">ตำแหน่ง</th>
                            <th class="py-2 px-2 text-center">สถานะ</th>
                            <th class="py-2 px-2 text-center">วันที่</th>
                            <th class="py-2 px-2 text-center">เวลาเข้างาน</th>
                            <th class="py-2 px-2 text-center">เวลาออกงาน</th>
                            <th class="py-2 px-2 text-center">เวลาเริ่มพัก</th>
                            <th class="py-2 px-2 text-center">เวลาเลิกพัก</th>
                        </tr>
                    </thead>

                    <tbody>
                        <tr *ngFor="let item of dataRow; let k = index"
                            class="hover:bg-slate-50/80 transition-colors duration-150 print:hover:bg-white">
                            <td class="py-2 px-2 text-center">{{ k + 1 }}</td>
                            <td class="py-2 px-2 text-left font-medium text-slate-900">{{item?.user?.user_id}}</td>
                            <td class="py-2 px-2 font-medium text-slate-900">
                             {{ item?.user?.first_name }} {{ item?.user?.last_name }}
                            </td>
                            <td class="py-2 px-2">{{ item?.user?.position?.name ?? '-' }}</td>

                            <!-- ป้ายสถานะ -->
                            <td class="py-2 px-2 text-center">
                                <ng-container [ngSwitch]="item?.attendance">
                                    <span *ngSwitchCase="'normal'" class="text-green-700 font-semibold">ปกติ</span>
                                    <span *ngSwitchCase="'late'" class="text-red-700 font-semibold">สาย</span>
                                    <span *ngSwitchCase="'miss'" class="text-red-700 font-semibold">ขาด</span>
                                    <span *ngSwitchCase="'leave'" class="text-amber-700 font-semibold">ลา</span>
                                    <span *ngSwitchCase="'leave_hour'"
                                        class="text-amber-700 font-semibold">ลาชั่วโมง</span>
                                    <span *ngSwitchCase="'off'" class="text-blue-700 font-semibold">หยุด</span>
                                    <span *ngSwitchDefault>-</span>
                                </ng-container>
                            </td>
                            <!-- วันที่/เวลา -->
                            <td class="py-2 px-2 text-center font-mono"
                                [class.text-red-700]="item?.attendance === 'late'"
                                [class.font-semibold]="item?.attendance === 'late'">
                                {{ item?.date | buddhistDate:'dd/MM/yyyy' ?? '-' }}
                            </td>
                            <td class="py-2 px-2 text-center font-mono"
                                [class.text-red-700]="item?.attendance === 'late' || item?.attendance === 'miss'"
                                [class.font-semibold]="item?.attendance === 'late' || item?.attendance === 'miss'"
                                [class.text-amber-700]="item?.attendance === 'leave'"
                                [class.font-semibold]="item?.attendance === 'leave'"
                                [class.text-amber-700]="item?.attendance === 'leave_hour'"
                                [class.font-semibold]="item?.attendance === 'leave_hour'"
                                [class.text-blue-700]="item?.attendance === 'blue'"
                                [class.font-semibold]="item?.attendance === 'blue'">
                                {{ item?.time_in ?? '-' }}
                            </td>
                            <td class="py-2 px-2 text-center font-mono">{{ item?.time_out ?? '-' }}</td>
                            <td class="py-2 px-2 text-center font-mono">{{ item?.time_brake_in ?? '-' }}</td>
                            <td class="py-2 px-2 text-center font-mono">{{ item?.time_brake_out ?? '-' }}</td>
                        </tr>

                        <tr *ngIf="!dataRow || dataRow?.length === 0">
                            <td class="py-6 px-4 text-center text-slate-500 italic" colspan="10">
                                ไม่พบข้อมูลการเข้างาน
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

    </div>
</div>