import { Route, Routes } from "@angular/router";
import { TimeAttendanceComponent } from "./time-attendance.component";
import { ListZkComponent } from "./list-zk/list.component";
import { AttendancePageComponent } from "../../user/attendance-page/attendance-page.component";

export default [
    {
        path: '',
        loadComponent: () => import('./time-attendance.component').then(m => m.TimeAttendanceComponent),
        children: [
            {
                path: 'list',
                loadComponent: () => import('./list/list.component').then(m => m.ListComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
            },

        ]
    },
    {
        path:'zk',
        component: ListZkComponent
    },
    {
        path:'zk-add',
        component: AttendancePageComponent
    }

] as Routes