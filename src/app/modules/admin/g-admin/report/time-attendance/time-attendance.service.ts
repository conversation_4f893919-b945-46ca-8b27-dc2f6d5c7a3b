import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { DataTablesResponse } from 'app/shared/datatable.types';
import { environment } from 'environments/environment';
import { Observable, switchMap, of } from 'rxjs';
const token = localStorage.getItem('accessToken') || null;
@Injectable({
    providedIn: 'root',
})
export class TimeAttendanceService {
    constructor(private _httpClient: HttpClient) { }
    httpOptionsFormdata = {
        headers: new HttpHeaders({ Authorization: `Bearer ${token}` }),
    };
    getByItemType(itemTypeId: number): Observable<any> {
        return this._httpClient
            .post(
                environment.API_URL + 'api/get_item',
                { item_type_id: itemTypeId },
                this.httpOptionsFormdata
            )
            .pipe(
                switchMap((response: any) => {
                    // Return a new observable with the response
                    return of(response);
                })
            );
    }
    getReport(data: any): Observable<any> {
        return this._httpClient
            .post(
                environment.API_URL + 'api/report_time_attendance',
                data,
                this.httpOptionsFormdata
            )
            .pipe(
                switchMap((response: any) => {
                    // Return a new observable with the response
                    return of(response);
                })
            );
    }

    syncTime(data: any): Observable<any> {
        return this._httpClient
            .get(
                environment.API_URL + 'api/punchlog', {
                params: {
                    start: data.date_start,
                    end: data.date_end
                }
            }

            )
            .pipe(
                switchMap((response: any) => {
                    // Return a new observable with the response
                    return of(response);
                })
            );
    }
    updateAttendance(data: any): Observable<any> {
        return this._httpClient
            .post(
                environment.API_URL + 'api/put_attendance', data



            )
            .pipe(
                switchMap((response: any) => {
                    // Return a new observable with the response
                    return of(response);
                })
            );
    }
    getExcel(data: any): Observable<any> {
        return this._httpClient
            .post(
                environment.API_URL + `api/export_time_in_out?personnel_id=${data.personnel_id}?date_start=${data.date_start}?date_end=${data.date_end}`,
                data,
                this.httpOptionsFormdata
            )
            .pipe(
                switchMap((response: any) => {
                    // Return a new observable with the response
                    return of(response);
                })
            );
    }
    getUser(id: any): Observable<any> {
        return this._httpClient.get(environment.API_URL + 'api/get_user', {
            params: { position: id },
        });
    }

    getZkPage(dataTablesParameters: any): Observable<DataTablesResponse> {
        return this._httpClient.post(environment.API_URL + 'api/zk_time_page', dataTablesParameters, this.httpOptionsFormdata).pipe(
            switchMap((response: any) => {
                return of(response.data);
            })
        );
    }

    saveAttendance(data: any): Observable<any> {
        return this._httpClient.post(
            `${environment.API_URL}api/put_zk_time`, data)
    }
}
