import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    OnDestroy,
    OnInit,
    ViewChild,
    ViewEncapsulation,
} from '@angular/core';
import {
    FormBuilder,
    FormControl,
    FormGroup,
    Validators,
} from '@angular/forms';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import {
    debounceTime,
    map,
    merge,
    Observable,
    Subject,
    switchMap,
    takeUntil,
} from 'rxjs';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from 'environments/environment';
import { AuthService } from 'app/core/auth/auth.service';
import { sortBy, startCase } from 'lodash-es';
import { AssetType, DataUser, UserPagination } from '../user.types';
import { UserService } from '../user.service';
import { MatTableDataSource } from '@angular/material/table';
import { ViewUserComponent } from '../view-user/view-user.component';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { DialogLeaveComponent } from '../dialog-leave/dialog-leave.component';
import { NgIf, NgFor, DatePipe } from '@angular/common';
import { MatProgressBar } from '@angular/material/progress-bar';
import { MatAnchor, MatIconButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { MatMenuTrigger, MatMenu, MatMenuItem } from '@angular/material/menu';
import { LineKeyComponent } from '../linekey/linekey.component';
import { MatTabChangeEvent, MatTabsModule } from '@angular/material/tabs';
import { BuddhistDatePipe } from 'app/modules/admin/pipes/buddhist-date.pipe';
import { UploadFileComponent } from '../../salary/upload-file/upload-file.component';
// import { ImportOSMComponent } from '../card/import-osm/import-osm.component';

@Component({
    selector: 'user-list',
    templateUrl: './list.component.html',
    styleUrls: ['./list.component.scss'],
    // encapsulation: ViewEncapsulation.None,
    // changeDetection: ChangeDetectionStrategy.OnPush,
    animations: fuseAnimations,
    imports: [NgIf, MatProgressBar, MatAnchor, MatIcon, DataTablesModule, NgFor, MatIconButton, MatMenuTrigger, MatMenu, MatMenuItem, DatePipe, MatTabsModule, BuddhistDatePipe]
})
export class UserListComponent implements OnInit, AfterViewInit, OnDestroy {
    @ViewChild(MatPaginator) private _paginator: MatPaginator;
    @ViewChild(MatSort) private _sort: MatSort;
    @ViewChild(DataTableDirective)
    dtElement!: DataTableDirective;
    displayedColumns: string[] = [
        'id',
        'user_id',
        'first_name',
        'last_name',
        'email',
        'status',
        'create_by',
        'created_at',
        'actions',
    ];
    dataSource: MatTableDataSource<DataUser>;
    dataRow: any = [];
    status: any[] = [
        { value: 'ปกติ', name: 'ปกติ' },
        { value: 'พักงาน', name: 'พักงาน' },
        { value: 'ลาออก', name: 'ลาออก' },
        { value: 'ไล่ออก', name: 'ไล่ออก' }
    ]

    form: FormGroup;
    products$: Observable<any>;
    asset_types: AssetType[];
    flashMessage: 'success' | 'error' | null = null;
    isLoading: boolean = false;
    searchInputControl: FormControl = new FormControl();
    selectedProduct: any | null = null;
    filterForm: FormGroup;
    tagsEditMode: boolean = false;
    private _unsubscribeAll: Subject<any> = new Subject<any>();
    env_path = environment.API_URL;
    public dtOptions: DataTables.Settings = {};
    private destroy$ = new Subject<any>();
    me: any | null;
    get roleType(): string {
        return 'marketing';
    }

    supplierId: string | null;
    pagination: UserPagination;

    /**
     * Constructor
     */
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseConfirmationService: FuseConfirmationService,
        private _formBuilder: FormBuilder,
        // private _Service: PermissionService,
        private _Service: UserService,
        private _matDialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _authService: AuthService
    ) { }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {
        this.form = this._formBuilder.group({
            status: 'ปกติ'
        })
        this.loadTable();
    }
    hiddenEdit() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 25);
        return menu.edit == 0;
    }
    hiddenDelete() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 25);
        return menu.delete == 0;
    }
    hiddenSave() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 25);
        return menu.save == 0;
    }
    pages = { current_page: 1, last_page: 1, per_page: 10, begin: 0 };
    loadTable(): void {
        const that = this;
        this.dtOptions = {
            pagingType: 'full_numbers',
            pageLength: 50,
            serverSide: true,
            processing: true,
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.11.3/i18n/th.json',
            },
            ajax: (dataTablesParameters: any, callback) => {
                dataTablesParameters.work_status = this.form.value.status;
                that._Service
                    .getuserpage(dataTablesParameters)
                    .subscribe((resp) => {
                        this.dataRow = resp.data;
                        this.pages.current_page = resp.current_page;
                        this.pages.last_page = resp.last_page;
                        this.pages.per_page = resp.per_page;
                        if (resp.current_page > 1) {
                            this.pages.begin =
                                resp.per_page * resp.current_page - 1;
                        } else {
                            this.pages.begin = 0;
                        }
                        callback({
                            recordsTotal: resp.total,
                            recordsFiltered: resp.total,
                            data: [],
                        });
                        this._changeDetectorRef.markForCheck();
                    });
            },
            columns: [
                { data: 'action', orderable: false },
                { data: 'id' },
                { data: 'user_id' },
                { data: 'prefix' },
                { data: 'first_name' },
                { data: 'last_name' },
                { data: 'sex' },
                { data: 'email' },
                { data: 'position' },
                { data: 'word_status' },
                { data: 'branch' },
                { data: 'created_at' },
            ],
        };
    }
    /**
     * After view init
     */
    ngAfterViewInit(): void {
        // if (this._sort && this._paginator) {
        //     // Set the initial sort
        //     this._sort.sort({
        //         id: 'id',
        //         start: 'asc',
        //         disableClear: true
        //     });
        //     // Mark for check
        //     this._changeDetectorRef.markForCheck();
        //     // If the user changes the sort order...
        //     this._sort.sortChange
        //         .pipe(takeUntil(this._unsubscribeAll))
        //         .subscribe(() => {
        //             // Reset back to the first page
        //             this._paginator.pageIndex = 0;
        //             // Close the details
        //             this.closeDetails();
        //         });
        //     // Get products if sort or page changes
        //     merge(this._sort.sortChange, this._paginator.page).pipe(
        //         switchMap(() => {
        //             this.closeDetails();
        //             this.isLoading = true;
        //             return this._Service.getProducts(
        //                 this._paginator.pageIndex + 1,
        //                 this._paginator.pageSize,
        //                 this._sort.active,
        //                 this._sort.direction,
        //                 this.filterForm.value?.searchInputControl,
        //                 this.filterForm.value?.asset_type == 'default' ? '' : this.filterForm.value?.asset_type,
        //                 this.supplierId
        //             );
        //         }),
        //         map(() => {
        //             this.isLoading = false;
        //         })
        //     ).subscribe();
        // }
    }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    resetForm(): void {
        this.filterForm.reset();
        this.filterForm.get('asset_type').setValue('default');
        this._changeDetectorRef.markForCheck();
    }

    /**
     * Close the details
     */
    closeDetails(): void {
        this.selectedProduct = null;
    }

    /**
     * Show flash message
     */
    showFlashMessage(type: 'success' | 'error'): void {
        // Show the message
        this.flashMessage = type;

        // Mark for check
        this._changeDetectorRef.markForCheck();

        // Hide it after 3 seconds
        setTimeout(() => {
            this.flashMessage = null;

            // Mark for check
            this._changeDetectorRef.markForCheck();
        }, 3000);
    }

    callDetail(productId: string): void {
        this._router.navigate(['marketing/brief-plan/' + productId]);
    }

    editBrief(productId: string): void {
        this._router.navigate(['marketing/brief-plan/edit/' + productId]);
    }
    edit(productId: string): void {
        this._router.navigate(['user/user-edit/' + productId]);
    }

    formCreate(): void {
        this._router.navigate(['user/user-form']);
    }

    importData() {
        // ตัวอย่าง: เปิด Dialog ให้อัปโหลดไฟล์ หรือเรียก API
        console.log('นำเข้าข้อมูล');
    }

    exportData() {
        // ตัวอย่าง: ดาวน์โหลดไฟล์ Excel หรือ CSV
        window.open(`${environment.API_URL}api/export_user`)
    }


    uploadFile() {
        const dialogRef = this._matDialog.open(UploadFileComponent, {
            width: '500px',
            height: 'auto',
            data: {
                form: ''
            },
        });

        dialogRef.afterClosed().subscribe((item) => {
            this.rerender();
            this._changeDetectorRef.markForCheck();
        });
    }



    editLeave(productId: number): void {
        const dialogRef = this._matDialog.open(DialogLeaveComponent, {
            width: '95vw',
            maxWidth: '900px',
            height: 'auto',
            panelClass: ['custom-dialog-panel', 'responsive-dialog'],
            autoFocus: false,
            data: {
                itemid: productId
            }
        });

        dialogRef.afterClosed().subscribe(() => {
            this.rerender();
            this._changeDetectorRef.markForCheck();
        });
    }


    openLineKeyDialog(id: number): void {
        const dialogRef = this._matDialog.open(LineKeyComponent, {
            width: '500px',
            data: id
        });

        dialogRef.afterClosed().subscribe(() => {
            this._changeDetectorRef.markForCheck(); // เผื่อ UI มีการเปลี่ยน
        });
    }

    openNewBrief(): void {
        this._router.navigateByUrl('marketing/brief-plan/brief/create');
    }

    openNewOrder(productId: string): void {
        // If the product is already selected...
        // if (this.selectedProduct && this.selectedProduct.id === productId) {
        //     // Close the details
        //     // this.closeDetails();
        //     return;
        // }

        this._router.navigate([
            'marketing/data/assets-list/new-order/' + productId,
        ]);
    }

    textStatus(status: string): string {
        return startCase(status);
    }

    // openImportOsm(): void {
    //     this._matDialog.open(ImportOSMComponent)
    // }

    ViewUser(data): void {
        const dialogRef = this._matDialog.open(ViewUserComponent, {
            width: '1000px',
            height: '650px',
            data: data,
        });
        dialogRef.afterClosed().subscribe((res) => { });
    }
    Delete(id) {
        const confirmation = this._fuseConfirmationService.open({
            title: 'ลบข้อมูล',
            message: 'คุณต้องการลบใช่หรือไม่ ',
            icon: {
                show: false,
                name: 'heroicons_outline:exclamation-triangle',
                color: 'warning',
            },
            actions: {
                confirm: {
                    show: true,
                    label: 'ยืนยัน',
                    color: 'danger',
                },
                cancel: {
                    show: true,
                    label: 'ยกเลิก',
                },
            },
            dismissible: true,
        });

        // Subscribe to the confirmation dialog closed action
        confirmation.afterClosed().subscribe((result) => {
            // If the confirm button pressed...
            if (result === 'confirmed') {
                this._Service
                    .delete(id)
                    .pipe(takeUntil(this.destroy$))
                    .subscribe((res: any) => {
                        if (res.code == 201) {
                            this._fuseConfirmationService
                                .open({
                                    title: 'ลบข้อมูล',
                                    message: 'ลบเรียบร้อย',
                                    icon: {
                                        show: true,
                                        name: 'heroicons_outline:check-circle',
                                        color: 'success',
                                    },
                                    actions: {
                                        confirm: {
                                            show: false,
                                            label: 'ตกลง',
                                            color: 'primary',
                                        },
                                        cancel: {
                                            show: false,
                                            label: 'ยกเลิก',
                                        },
                                    },
                                    dismissible: true,
                                })
                                .afterClosed()
                                .subscribe((res) => {
                                    this.rerender();
                                });
                        }
                    });
            }
        });
    }

    rerender(): void {
        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
            dtInstance.ajax.reload();
        });
    }

    onTabChange(event: MatTabChangeEvent): void {
        const selectedStatus = this.status[event.index].value;
        this.form.patchValue({ status: selectedStatus });
        this.rerender();
    }

}
