<div class="sm:absolute sm:inset-0 flex flex-col flex-auto min-w-0 sm:overflow-auto bg-card dark:bg-transparent">

    <!-- Header -->
    <div
        class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b">
        <!-- Loader -->
        <div class="absolute inset-x-0 bottom-0" *ngIf="isLoading">
            <mat-progress-bar [mode]="'indeterminate'"></mat-progress-bar>
        </div>
        <!-- Title -->
        <div class="text-4xl font-extrabold tracking-tight">รายการพนักงาน</div>
        <!-- Actions -->
        <!-- Actions -->
        <div class="flex shrink-0 items-center mt-6 sm:mt-0 sm:ml-4 space-x-2 gap-2">
            <!-- ปุ่มนำเข้าข้อมูล -->
            <button mat-flat-button
                class="bg-blue-600 hover:bg-blue-700 text-white rounded-xl px-4 py-2 min-w-[140px] shadow transition-all flex items-center justify-center gap-2"
                (click)="uploadFile()">
                <mat-icon class="text-white">upload_file</mat-icon>
                <span>นำเข้าข้อมูล</span>
            </button>

            <!-- ปุ่มนำออกข้อมูล -->
            <button mat-flat-button
                class="bg-green-600 hover:bg-green-700 text-white rounded-xl px-4 py-2 min-w-[140px] shadow transition-all flex items-center justify-center gap-2"
                (click)="exportData()">
                <mat-icon class="text-white">download</mat-icon>
                <span>นำออกข้อมูล</span>
            </button>

            <button mat-flat-button *ngIf="!hiddenSave()"
                class="bg-yellow-600 hover:bg-yellow-700 text-white rounded-xl px-4 py-2 min-w-[140px] shadow transition-all flex items-center justify-center gap-2"
                (click)="formCreate()">
                <mat-icon class="text-white" [svgIcon]="'heroicons_outline:plus'"></mat-icon>
                <span>เพิ่มพนักงาน</span>
            </button>
            <!-- ปุ่มเพิ่มพนักงาน -->

        </div>

    </div>

    <!-- Main -->
    <div class="flex flex-col flex-auto min-w-0 bg-gray-100 dark:bg-transparent">

        <div class="flex flex-col p-3 sm:p-6">
            <div class="  bg-white">
                <!-- Products list -->
                <div class="flex flex-col flex-auto p-5 sm:overflow-auto">
                    <mat-tab-group (selectedTabChange)="onTabChange($event)" mat-align-tabs="start"
                        mat-stretch-tabs="false">
                        <mat-tab *ngFor="let s of status; let i = index" [label]="s.name">
                        </mat-tab>
                    </mat-tab-group>
                    <table datatable [dtOptions]="dtOptions"
                        class="table w-full text-left text-gray-500 overflow-hidden">
                        <thead class="bg-gray-300 text-black">
                            <tr>
                                <th class="whitespace-nowrap">จัดการ</th>
                                <th class="whitespace-nowrap">รูป</th>
                                <th class="whitespace-nowrap">รหัสพนักงาน</th>
                                <th class="whitespace-nowrap">คำนำหน้า</th>
                                <th class="whitespace-nowrap">ชื่อ</th>
                                <th class="whitespace-nowrap">นามสกุล</th>
                                <th class="whitespace-nowrap">เพศ</th>
                                <th class="whitespace-nowrap">อีเมล</th>
                                <th class="whitespace-nowrap">ตำแหน่ง</th>
                                <th class="whitespace-nowrap">สาขา</th>
                                <th class="whitespace-nowrap">วันที่เริ่มทำงาน</th>
                                <th class="whitespace-nowrap">สถานะ</th>
                                <th class="whitespace-nowrap">วันที่สร้าง</th>
                            </tr>
                        </thead>
                        <tbody *ngIf="dataRow?.length != 0">
                            <tr *ngFor="let item of dataRow; let i = index"
                                class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 text-md whitespace-nowrap">
                                <td class="w-20">
                                    <button mat-icon-button [matMenuTriggerFor]="menu"
                                        aria-label="icon-button with a menu">
                                        <mat-icon svgIcon="heroicons_solid:cog-6-tooth"></mat-icon>
                                    </button>
                                    <mat-menu #menu="matMenu" class="bg-white">
                                        <button mat-menu-item (click)="edit(item.id)" [disabled]="hiddenEdit()">
                                            <mat-icon svgIcon="heroicons_solid:pencil-square"></mat-icon>
                                            <span>แก้ไข</span>
                                        </button>
                                        <button mat-menu-item (click)="editLeave(item.id)" [disabled]="hiddenEdit()">
                                            <mat-icon svgIcon="heroicons_solid:paper-airplane"></mat-icon>
                                            <span>วันลา</span>
                                        </button>
                                        <button mat-menu-item (click)="openLineKeyDialog(item.id)"
                                            [disabled]="hiddenEdit()">
                                            <mat-icon svgIcon="heroicons_solid:key"></mat-icon>
                                            <span>Line key</span>
                                        </button>
                                        <button mat-menu-item (click)="Delete(item.id)" [disabled]="hiddenDelete()">
                                            <mat-icon svgIcon="heroicons_solid:trash"></mat-icon>
                                            <span>ลบ</span>
                                        </button>
                                    </mat-menu>
                                </td>
                                <td class="whitespace-nowrap min-w-15 w-full items-center">
                                    <img [src]="item.image ?? 'assets/images/no_image.jpeg'" alt="Profile"
                                        class="w-[60px] h-[60px] rounded-full object-cover border-2 items-center" />
                                </td>
                                <td class="w-10">{{ item.user_id }}</td>
                                <td class="whitespace-nowrap min-w-30 w-full">{{ item?.prefix ?? '-' }}</td>
                                <td class="whitespace-nowrap min-w-30 w-full">{{ item?.first_name }}</td>
                                <td class="whitespace-nowrap min-w-30 w-full">{{ item?.last_name }}</td>
                                <td class=" min-w-20 w-full">
                                    <div class=" text-slate-500 text-md font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-green-200 dark:text-green-900"
                                        *ngIf="item?.sex === 'M'">
                                        ผู้ชาย
                                    </div>
                                    <div class=" text-slate-500 text-md font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-green-200 dark:text-green-900"
                                        *ngIf="item?.sex === 'F'">
                                        ผู้หญิง
                                    </div>
                                </td>
                                <td class="whitespace-nowrap min-w-30 w-full">{{ item?.email ?? '-' }}
                                </td>
                                <td class="whitespace-nowrap min-w-30 w-full">{{
                                    item.position?.name ?? '-' }}</td>
                                <td class="whitespace-nowrap min-w-30 w-full">{{
                                    item?.branch.name ?? '-'}}</td>

                                <td class="whitespace-nowrap min-w-30 w-full">
                                    {{ item?.register_date | buddhistDate}}</td>
                                <td class="whitespace-nowrap min-w-30 w-full">
                                    <div *ngIf="item.work_status === 'ปกติ'">
                                        <!-- เปิดการใช้งาน -->
                                        <span
                                            class="bg-green-100 text-green-800 text-md font-extrabold mr-2 px-2.5 py-0.5 rounded dark:bg-green-200 dark:text-green-900">
                                            ปกติ
                                        </span>
                                    </div>
                                    <div *ngIf="item.work_status === 'พักงาน'">
                                        <!-- ปิดการใช้งาน -->
                                        <span
                                            class="bg-orange-100 text-orange-800 text-md font-extrabold mr-2 px-2.5 py-0.5 rounded dark:bg-orange-200 dark:text-orange-900">
                                            พักงาน
                                        </span>
                                    </div>
                                    <div *ngIf="item.work_status === 'ลาออก'">
                                        <!-- ปิดการใช้งาน -->
                                        <span
                                            class="bg-red-100 text-red-800 text-md font-extrabold mr-2 px-2.5 py-0.5 rounded dark:bg-red-200 dark:text-red-900">
                                            ลาออก
                                        </span>
                                    </div>
                                </td>
                                <td class="whitespace-nowrap min-w-30 w-full">
                                    {{ item.created_at | buddhistDate: 'dd/MM/yyyy HH:mm' }}
                                </td>
                            </tr>
                        </tbody>
                        <tbody *ngIf="dataRow?.length == 0">
                            <tr>
                                <td colspan="12" class="no-data-available text-center">ไม่มีข้อมูล !</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>