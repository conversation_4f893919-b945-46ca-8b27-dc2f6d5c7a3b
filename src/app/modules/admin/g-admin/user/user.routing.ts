import { Routes } from '@angular/router';
import { UserComponent } from './user.component';
import { UserListComponent } from './list/list.component';
import { ProfileUserComponent } from './profile-user/profile-user.component';
import { UserFormComponent } from './form/user-form.component';
import { CompanyFormComponent } from './company/company.component';

export default [
    {
        path: '',
        component: UserComponent,
        children: [
            { path:'list' , component: UserListComponent},
            { path: 'profile', component: ProfileUserComponent},
            {
                path: 'create-user',
                loadComponent: () => import('./create-user/create-user.component').then(m => m.CreateUserComponent),
                // resolve: {
                //     permission: PermissionProductsResolver,
                //     department: DepartmentResolver,
                //     resolveGet: PositionResolve,
                //     branch: BranchResolver,
                // }
            },
            {
                path: 'user-form',
                component: UserFormComponent
            },
            {
                path: 'user-edit/:id',
                component: UserFormComponent
            },
            {
                path: 'company',
                component: CompanyFormComponent
            },
            {
                path: 'company-edit/:id',
                component: CompanyFormComponent
            },
            {
                path: 'edit/:id',
                loadComponent: () => import('./edit-user/edit-user.component').then(m => m.EditUserComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
            },
            {
                path: 'view/:id',
                loadComponent: () => import('./view-user/view-user.component').then(m => m.ViewUserComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
            },
            {
                path: 'profile',
                loadComponent: () => import('./profile-user/profile-user.component').then(m => m.ProfileUserComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
            },

            {
                path: 'profileuseradmin',
                loadComponent: () => import('./profile-useradmin/profile-useradmin.component').then(m => m.ProfileUseradminComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
            },

            {
                path: 'profile1',
                loadComponent: () => import('./profile/profile.component').then(m => m.ProfileComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
            },

            {
                path: 'profile2',
                loadComponent: () => import('./profile2/profile2.component').then(m => m.Profile2Component),
            },

            {
                path: 'profile3',
                loadComponent: () => import('./profile3/profile3.component').then(m => m.Profile3Component),
            },

            {
                path: 'profile-firstpage',
                loadComponent: () => import('./profile-firstpage/profile-firstpage.component').then(m => m.ProfileFirstpageComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
            },

            {
                path: 'edit-profile',
                loadComponent: () => import('./edit-profile/edit-profile.component').then(m => m.EditProfileComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
            },
            {
                path: 'change-pwd/:id',
                loadComponent: () => import('./change-pwd/change-pwd.component').then(m => m.ChangePwdComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
            },
            {
                path: 'withdrawsalary',
                loadComponent: () => import('./withdrawsalary/new.component').then(m => m.WithdrawsalaryComponent),
            },
            {
                path: 'leave',
                loadComponent: () => import('./leave/new.component').then(m => m.NewLeaveComponent),
            },
            {
                path: 'ot',
                loadComponent: () => import('./ot/ot.component').then(m => m.OtListComponent),
            },
            {
                path: 'salary',
                loadComponent: () => import('./salary/salary.component').then(m => m.SalaryListComponent),
            },
        ]
     }

] as Routes



















