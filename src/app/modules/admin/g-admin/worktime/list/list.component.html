<div class="sm:absolute sm:inset-0 flex flex-col flex-auto min-w-0 sm:overflow-auto bg-card dark:bg-transparent">

    <!-- Header -->
    <div
        class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b">
        <!-- Loader -->
        <div class="absolute inset-x-0 bottom-0" *ngIf="isLoading">
            <mat-progress-bar [mode]="'indeterminate'"></mat-progress-bar>
        </div>
        <!-- Title -->
        <div class="text-4xl font-extrabold tracking-tight">รายงานรายวันของพนักงาน</div>

        <!-- Actions -->
        <div class="flex gap-2">
            <button mat-flat-button
                class="bg-gray-700 text-white hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
                (click)="Search()">
                <mat-icon [svgIcon]="'heroicons_outline:magnifying-glass'"></mat-icon>
                <span class="ml-2 mr-1">ค้นหา</span>
            </button>

            <button mat-flat-button
                class="bg-gray-700 text-white hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
                type="reset">
                <mat-icon [svgIcon]="'heroicons_outline:arrow-path'"></mat-icon>
                <span class="ml-2 mr-1">ล้าง</span>
            </button>

            <button mat-flat-button color="primary" class="rounded-lg py-2 px-4" (click)="exportDailyReportPDF()">
                <mat-icon [svgIcon]="'heroicons_outline:document'"></mat-icon>
                <span class="ml-2 mr-1">PDF</span>
            </button>
        </div>
    </div>
    <!-- Main -->
    <form [formGroup]="formData">
        <div class="flex flex-col flex-auto min-w-0 bg-gray-100 dark:bg-transparent ">
            <div class="flex flex-col p-3 sm:p-6 bg-card">
                <div class="border-b flex flex-col md:flex-row gap-4 w-full p-4">
                    <div class="flex flex-col w-full">
                        <mat-label for="name"
                            class="text-gray-800 text-lg font-bold leading-tight tracking-normal mb-1">เลือกพนักงาน</mat-label>
                        <ng-container *ngIf="UserList">
                            <app-personnel-autocomplete [itemtypeData]="UserList"
                                (personnelSelected)="onPersonnelSelected($event)">
                            </app-personnel-autocomplete>
                        </ng-container>
                    </div>
                    <mat-form-field class="w-full md:w-full" [ngClass]="formFieldHelpers">
                        <mat-label for="name"
                            class="text-gray-800 text-lg font-bold leading-tight tracking-normal">เลือกวันที่</mat-label>
                        <mat-date-range-input [rangePicker]="picker">
                            <input matStartDate formControlName="date_start" placeholder="วันที่เริ่มต้น">
                            <input matEndDate formControlName="date_end" placeholder="วันที่สิ้นสุด">
                        </mat-date-range-input>
                        <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
                        <mat-date-range-picker #picker></mat-date-range-picker>
                    </mat-form-field>

                    <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
                        <mat-label for="name"
                            class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/3 mt-4">ประเภท</mat-label>
                        <mat-select [formControlName]="'type'">
                            <mat-option [value]="null">ทั้งหมด </mat-option>
                            <mat-option value="normal">ปกติ </mat-option>
                            <mat-option value="late">มาสาย</mat-option>
                            <mat-option value="miss">ขาด</mat-option>
                            <mat-option value="leave">ลา</mat-option>
                            <mat-option value="leave_hour">ลา(ชั่วโมง)</mat-option>
                            <mat-option value="off">หยุด</mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
                <div class="flex flex-col flex-auto p-5 sm:overflow-auto overflow-x-scroll"> 
                    <div class="overflow-x-auto rounded-none border border-slate-200 dark:border-slate-700 p-6">
                        <table datatable [dtOptions]="dtOptions"
                            class="min-w-full table-auto text-sm text-slate-700 dark:text-slate-200">
                            <thead class="bg-slate-50/80 dark:bg-slate-800/60 backdrop-blur sticky top-0 z-10">
                                <tr class="text-left text-slate-600 dark:text-slate-300">
                                    <th class="h-10 w-1/12 px-4 py-3 font-semibold uppercase tracking-wide">ลำดับ</th>
                                    <th class="w-3/12 px-4 py-3 font-semibold uppercase tracking-wide">ชื่อ</th>
                                    <th class="w-1/12 px-4 py-3 text-center font-semibold uppercase tracking-wide">สถานะ
                                    </th>
                                    <th class="w-2/12 px-4 py-3 font-semibold uppercase tracking-wide">วันที่</th>
                                    <th class="w-2/12 px-4 py-3 font-semibold uppercase tracking-wide">เวลาเข้า</th>
                                    <th class="w-2/12 px-4 py-3 font-semibold uppercase tracking-wide">เวลาออก</th>
                                    <th class="w-1/12 px-4 py-3 font-semibold uppercase tracking-wide">ชั่วโมง</th>
                                </tr>
                            </thead>

                            <tbody *ngIf="dataRow?.length !== 0"
                                class="divide-y divide-slate-100 dark:divide-slate-700">
                                <tr *ngFor="let item of dataRow; let i = index"
                                    class="hover:bg-slate-50 dark:hover:bg-slate-800/40 transition-colors">
                                    <td class="px-4 py-2 align-middle">{{ pages.begin + (i + 1) }}</td>
                                    <td class="px-4 py-2 align-middle">
                                        {{ item.user ? item.user?.first_name : '-' }}
                                        {{ item.user ? item.user?.last_name : '-' }}
                                    </td>

                                    <!-- สถานะ -->
                                    <td class="px-4 py-2 align-middle text-center">
                                        <span *ngIf="item?.type === 'normal'"
                                            class="inline-flex items-center rounded-full bg-green-100 text-green-700 dark:bg-green-200/20 dark:text-green-300 px-2 py-0.5 text-xs font-bold">
                                            ปกติ
                                        </span>
                                        <span *ngIf="item?.type === 'late'"
                                            class="inline-flex items-center rounded-full bg-red-100 text-red-700 dark:bg-red-200/20 dark:text-red-300 px-2 py-0.5 text-xs font-bold">
                                            สาย
                                        </span>
                                        <span *ngIf="item?.type === 'miss'"
                                            class="inline-flex items-center rounded-full bg-red-100 text-red-700 dark:bg-red-200/20 dark:text-red-300 px-2 py-0.5 text-xs font-bold">
                                            ขาด
                                        </span>
                                        <span *ngIf="item?.type === 'leave'"
                                            class="inline-flex items-center rounded-full bg-amber-100 text-amber-700 dark:bg-amber-200/20 dark:text-amber-300 px-2 py-0.5 text-xs font-bold">
                                            ลา
                                        </span>
                                        <span *ngIf="item?.type === 'leave_hour'"
                                            class="inline-flex items-center rounded-full bg-amber-100 text-amber-700 dark:bg-amber-200/20 dark:text-amber-300 px-2 py-0.5 text-xs font-bold">
                                            ลา (ชั่วโมง)
                                        </span>
                                        <span *ngIf="item?.type === 'off'"
                                            class="inline-flex items-center rounded-full bg-blue-100 text-blue-700 dark:bg-blue-200/20 dark:text-blue-300 px-2 py-0.5 text-xs font-bold">
                                            หยุด
                                        </span>
                                    </td>

                                    <td class="px-4 py-2 align-middle">
                                        {{ item.date !== null ? (item.date | date: 'dd-MM-yyyy') : 'No Data' }}
                                    </td>

                                    <td class="px-4 py-2 align-middle">
                                        {{ item.time_in !== null ? item.time_in : '-' }}
                                    </td>
                                    <td class="px-4 py-2 align-middle">
                                        {{ item.time_out !== null ? item.time_out : '-' }}
                                    </td>

                                    <td class="px-4 py-2 align-middle text-right">
                                        {{ item.qty_hour !== null ? item.qty_hour : 0 }}
                                    </td>
                                </tr>
                            </tbody>

                            <tbody *ngIf="dataRow?.length == 0">
                                <tr>
                                    <td colspan="7" class="px-4 py-6 text-center text-slate-500">ไม่มีข้อมูล !</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                </div>
            </div>
        </div>
    </form>
</div>