import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    OnDestroy,
    OnInit,
    ViewChild,
    ViewEncapsulation,
} from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import {
    debounceTime,
    map,
    merge,
    Observable,
    Subject,
    switchMap,
    takeUntil,
} from 'rxjs';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from 'environments/environment';
// import { ImportOSMComponent } from '../card/import-osm/import-osm.component';
import { MatTableDataSource } from '@angular/material/table';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { WorktimeService } from '../worktime.service';
import { EditComponent } from '../edit/edit.component';
import { NewComponent } from '../new/new.component';
import { AutofillMonitor } from '@angular/cdk/text-field';
import moment from 'moment';
import { NgIf, NgFor, DatePipe, CommonModule } from '@angular/common';
import { MatProgressBar, MatProgressBarModule } from '@angular/material/progress-bar';
import { MatButton, MatButtonModule } from '@angular/material/button';
import { MatIcon, MatIconModule } from '@angular/material/icon';
import { PersonnelAutocompleteComponent } from '../../../../../shared/personnel-autocomplete/personnel-autocomplete.component';
import { MatFormField, MatFormFieldModule, MatLabel, MatSuffix } from '@angular/material/form-field';
import { MatDateRangeInput, MatStartDate, MatEndDate, MatDatepickerToggle, MatDateRangePicker, MatDatepickerModule } from '@angular/material/datepicker';
import { MatSelect, MatSelectModule } from '@angular/material/select';
import { MatOption } from '@angular/material/autocomplete';
import { MatNativeDateModule } from '@angular/material/core';

@Component({
    selector: '',
    templateUrl: './list.component.html',
    styleUrls: ['./list.component.scss'],
    // encapsulation: ViewEncapsulation.None,
    // changeDetection: ChangeDetectionStrategy.OnPush,
    animations: fuseAnimations,
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        MatButtonModule,
        MatIconModule,
        MatFormFieldModule,
        MatDatepickerModule,
        MatNativeDateModule,
        MatSelectModule,
        MatProgressBarModule,
        DataTablesModule,
        // Pipe & Directive
        DatePipe,
        // Component
        PersonnelAutocompleteComponent,
    ]

})
export class ListComponent implements OnInit, AfterViewInit, OnDestroy {
    @ViewChild(DataTableDirective)
    dtElement!: DataTableDirective;
    public dtOptions: DataTables.Settings = {};
    public dataRow: any[];
    public UserList: any[];
    formData: FormGroup;
    formFieldHelpers: string[] = ['fuse-mat-dense'];
    private destroy$ = new Subject<any>();
    // dataRow: any = []
    @ViewChild(MatPaginator) _paginator: MatPaginator;
    @ViewChild(MatSort) private _sort: MatSort;

    flashMessage: 'success' | 'error' | null = null;
    isLoading: boolean = false;
    searchInputControl: FormControl = new FormControl();
    selectedProduct: any | null = null;
    filterForm: FormGroup;
    tagsEditMode: boolean = false;
    months = [
        { key: '01', name: 'มกราคม' },
        { key: '02', name: 'กุมภาพันธ์' },
        { key: '03', name: 'มีนาคม' },
        { key: '04', name: 'เมษายน' },
        { key: '05', name: 'พฤษภาคม' },
        { key: '06', name: 'มิถุนายน' },
        { key: '07', name: 'กรกฎาคม' },
        { key: '08', name: 'สิงหาคม' },
        { key: '09', name: 'กันยายน' },
        { key: '10', name: 'ตุลาคม' },
        { key: '11', name: 'พฤศจิกายน' },
        { key: '12', name: 'ธันวาคม' },
    ];
    years: number[] = [];

    private _unsubscribeAll: Subject<any> = new Subject<any>();
    /**
     * Constructor
     */
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseConfirmationService: FuseConfirmationService,
        private _formBuilder: FormBuilder,
        private _Service: WorktimeService,
        private _matDialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute
    ) {

        const currentYear = new Date().getFullYear(); // ดึงปีปัจจุบัน
        const startYear = currentYear - 5; // ปีเริ่มต้น
        const endYear = currentYear + 5; // ปีสิ้นสุด
        for (let year = startYear; year <= endYear; year++) {
            this.years.push(year);
        }
        this.formData = this._formBuilder.group({
            year: currentYear,
            month: new Date().toLocaleString('en-US', { month: '2-digit' }),
            user_id: '',
            type: '',
            date_start: moment().format('YYYY-MM-DD'),
            date_end: moment().format('YYYY-MM-DD'),
        });
        this._Service.getUser().subscribe((resp: any) => {
            this.UserList = resp.data;
            this._changeDetectorRef.markForCheck();
        });
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    onPersonnelSelected(selectedPersonnel: any): void {
        this.formData.patchValue({
            user_id: selectedPersonnel.id,
        })
    }
    ngOnInit(): void {


        // สร้าง array ของปี

        this.loadTable();

    }
    hiddenEdit() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 26);
        return menu.edit == 0;
    }
    hiddenDelete() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 26);
        return menu.delete == 0;
    }
    hiddenSave() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 26);
        return menu.save == 0;
    }
    /**
     * After view init
     */
    ngAfterViewInit(): void { }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Close the details
     */

    /**
     * Show flash message
     */
    showFlashMessage(type: 'success' | 'error'): void {
        // Show the message
        this.flashMessage = type;

        // Mark for check
        this._changeDetectorRef.markForCheck();

        // Hide it after 3 seconds
        setTimeout(() => {
            this.flashMessage = null;

            // Mark for check
            this._changeDetectorRef.markForCheck();
        }, 3000);
    }

    Edit(itemId: string) {
        const dialogRef = this._matDialog.open(EditComponent, {
            width: '800px',
            height: '600px',
            data: {
                itemid: itemId,
            },
        });
        dialogRef.afterClosed().subscribe((item) => {
            this.rerender();
            this._changeDetectorRef.markForCheck();
        });
    }

    New(data: any) {
        const dialogRef = this._matDialog.open(NewComponent, {
            width: '400px',
            height: '600px',
            data: data,
        });

        dialogRef.afterClosed().subscribe((item) => {
            this.rerender();
            this._changeDetectorRef.markForCheck();
        });
    }

    Search() {
        this.rerender();
    }

    Clear() {
        this.formData.reset();
    }

    pages = { current_page: 1, last_page: 1, per_page: 10, begin: 0 };
    loadTable(): void {
        const that = this;
        this.dtOptions = {
            pagingType: 'full_numbers',
            pageLength: 100,
            serverSide: true,
            processing: true,
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.11.3/i18n/th.json',
            },
            ajax: (dataTablesParameters: any, callback) => {
                dataTablesParameters.date_start = moment(this.formData.value.date_start).format('YYYY-MM-DD');
                dataTablesParameters.date_end = moment(this.formData.value.date_end).format('YYYY-MM-DD');
                // dataTablesParameters.year = this.formData.value.year;
                // dataTablesParameters.year = this.formData.value.year;
                // dataTablesParameters.month = this.formData.value.month;
                dataTablesParameters.user_id = this.formData.value.user_id;
                dataTablesParameters.type = this.formData.value.type;

                that._Service
                    .getItemTypePage(dataTablesParameters)
                    .subscribe((resp) => {
                        this.dataRow = resp.data;
                        this.pages.current_page = resp.current_page;
                        this.pages.last_page = resp.last_page;
                        this.pages.per_page = resp.per_page;
                        if (resp.current_page > 1) {
                            this.pages.begin =
                                resp.per_page * resp.current_page - 1;
                        } else {
                            this.pages.begin = 0;
                        }
                        callback({
                            recordsTotal: resp.total,
                            recordsFiltered: resp.total,
                            data: [],
                        });
                        this._changeDetectorRef.markForCheck();
                    });
            },
            columns: [
                // { data: 'action', orderable: false },
                { data: 'no' },
                { data: 'name' },
                { data: 'status' },
                { data: 'type' },
                { data: 'date' },
                { data: 'time_start' },
                { data: 'time_out' },
            ],
        };
    }

    rerender(): void {
        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
            dtInstance.ajax.reload();
        });
    }

    Delete(id) {
        const confirmation = this._fuseConfirmationService.open({
            title: 'ลบเงินหัก',
            message: 'คุณต้องการลบเงินเพิ่มใช่หรือไม่ ',
            icon: {
                show: false,
                name: 'heroicons_outline:exclamation-triangle',
                color: 'warning',
            },
            actions: {
                confirm: {
                    show: true,
                    label: 'ยืนยัน',
                    color: 'danger',
                },
                cancel: {
                    show: true,
                    label: 'ยกเลิก',
                },
            },
            dismissible: true,
        });

        // Subscribe to the confirmation dialog closed action
        confirmation.afterClosed().subscribe((result) => {
            // If the confirm button pressed...
            if (result === 'confirmed') {
                this._Service
                    .delete(id)
                    .pipe(takeUntil(this.destroy$))
                    .subscribe((res: any) => {
                        if (res.code == 201) {
                            this._fuseConfirmationService
                                .open({
                                    title: 'ลบข้อมูลประเภทเงินเพิ่ม',
                                    message: 'บันทึกเรียบร้อย',
                                    icon: {
                                        show: true,
                                        name: 'heroicons_outline:check-circle',
                                        color: 'success',
                                    },
                                    actions: {
                                        confirm: {
                                            show: false,
                                            label: 'ตกลง',
                                            color: 'primary',
                                        },
                                        cancel: {
                                            show: false,
                                            label: 'ยกเลิก',
                                        },
                                    },
                                    dismissible: true,
                                })
                                .afterClosed()
                                .subscribe((res) => {
                                    this.rerender();
                                });
                        }
                    });
            }
        });
    }

    exportDailyReportPDF() {
        let formValue = this.formData.value

        if (formValue.usre_id) {
            alert('กรุณาเลือกพนักงาน')
            return;
        }
        formValue.date_start = moment(formValue.date_start).format('YYYY-MM-DD');
        formValue.date_end = moment(formValue.date_end).format('YYYY-MM-DD');
        window.open(`${environment.API_URL}api/report_DailyReport?user_id=${formValue.user_id}&type=${formValue.type}&date_start=${formValue.date_start}&date_end=${formValue.date_end}`)
    }

    exportDailyReportExcel() {
        let formValue = this.formData.value

        if (formValue.usre_id) {
            alert('กรุณาเลือกพนักงาน')
            return;
        }
        formValue.date_start = moment(formValue.date_start).format('YYYY-MM-DD');
        formValue.date_end = moment(formValue.date_end).format('YYYY-MM-DD');
        window.open(`${environment.API_URL}api/Export_DailyReport_Excel?user_id=${formValue.user_id}&type=${formValue.type}&date_start=${formValue.date_start}&date_end=${formValue.date_end}`)
    }
}
