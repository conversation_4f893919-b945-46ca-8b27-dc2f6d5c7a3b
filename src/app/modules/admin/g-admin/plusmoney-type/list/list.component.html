<div class="sm:absolute sm:inset-0 flex flex-col flex-auto min-w-0 sm:overflow-auto bg-card dark:bg-transparent">

    <!-- Header -->
    <div
        class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b">
        <!-- Loader -->
        <div class="absolute inset-x-0 bottom-0" *ngIf="isLoading">
            <mat-progress-bar [mode]="'indeterminate'"></mat-progress-bar>
        </div>
        <!-- Title -->
        <div class="text-4xl font-extrabold tracking-tight">ประเภทเงินเพิ่ม</div>
        <!-- Actions -->
        <div class="flex shrink-0 items-center mt-6 sm:mt-0 sm:ml-4">

            <!-- Add product button -->
            <a (click)="New()" class="ml-4" mat-flat-button [color]="'primary'" *ngIf="!hiddenSave()">
                <mat-icon [svgIcon]="'heroicons_outline:plus'"></mat-icon>
                <span class="ml-2 mr-1">สร้างประเภทเงินเพิ่ม</span>
            </a>
        </div>
    </div>

    <!-- Main -->
    <div class="flex flex-col flex-auto min-w-0 bg-gray-100 dark:bg-transparent">

        <div class="flex flex-col p-3 sm:p-6">
            <div class="overflow-auto  bg-white shadow sm:rounded-lg">
                <!-- Products list -->

                <div class="flex flex-col flex-auto p-5 sm:overflow-auto overflow-x-scroll">

                    <table datatable [dtOptions]="dtOptions"
                        class="table w-full text-left text-gray-500 overflow-hidden">
                        <thead class="bg-gray-300 text-black">
                            <tr>
                                <th class="w-[10%]">จัดการ</th>
                                <th class="w-[5%]">ลำดับ</th>
                                <th class="w-[10%]">ประเภท</th>
                                <th class="w-[15%]">ชื่อ</th>
                                <th class="w-[30%]">รายละเอียด</th>
                                <th class="w-[10%]">สถานะ</th>
                            </tr>
                        </thead>
                        <tbody *ngIf="dataRow?.length != 0">
                            <tr *ngFor="let item of dataRow; let i = index">
                                <td>
                                    <button mat-icon-button (click)="Edit(item)">
                                        <mat-icon>edit</mat-icon>
                                    </button>
                                    <button mat-icon-button (click)="Delete(item.id)" [disabled]="hiddenDelete()">
                                        <mat-icon>delete</mat-icon>
                                    </button>
                                </td>
                                <td>{{ pages.begin + (i + 1) }}</td>
                                <td>
                                    <div class="rounded-lg p-2 w-fit font-semibold"
                                        [ngClass]="{'text-yellow-600 bg-yellow-200': item.type === 'Once','text-purple-600  bg-purple-200': item.type === 'All Month'}">
                                        {{item.type === 'Once' ? 'เพิ่มเงินรายครั้ง' :item.type === 'All Month' ?
                                        'เพิ่มเงินประจำ' :'-'}}
                                    </div>
                                </td>
                                <td>{{ item.name ?? '-' }}</td>
                                <td>{{ item.description }}</td>
                                <td>
                                    <div
                                        class="rounded-lg p-2 w-full font-semibold flex justify-center items-center gap-1">
                                        <mat-icon
                                            [svgIcon]="item.view_in_slip === 1 ? 'heroicons_solid:eye' : 'heroicons_solid:eye-slash'"
                                            [ngClass]="{'text-green-600': item.view_in_slip === 1,'text-red-600': item.view_in_slip === 0}"
                                            class="w-8 h-8"></mat-icon>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                        <tbody *ngIf="dataRow?.length == 0">
                            <tr>
                                <td colspan="8" class="no-data-available text-center">ไม่มีข้อมูล !</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

</div>