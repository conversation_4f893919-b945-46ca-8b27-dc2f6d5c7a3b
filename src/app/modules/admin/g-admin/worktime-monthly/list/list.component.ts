import {
    After<PERSON><PERSON>w<PERSON>nit,
    ChangeDetector<PERSON><PERSON>,
    <PERSON>mponent,
    <PERSON><PERSON><PERSON><PERSON>,
    OnInit,
    ViewChild,
} from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import {
    Subject,
} from 'rxjs';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from 'environments/environment';
// import { ImportOSMComponent } from '../card/import-osm/import-osm.component';
import { DataTableDirective } from 'angular-datatables';
import { WorkTimeMonthlyService } from '../worktime-monthly.service';

import { MY_DATE_FORMATS } from 'app/modules/date-formats';
import { MAT_DATE_FORMATS } from '@angular/material/core';
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import { NgIf, NgFor, CommonModule } from '@angular/common';
import { MatProgressBar } from '@angular/material/progress-bar';
import { MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { PersonnelAutocompleteComponent } from '../../../../../shared/personnel-autocomplete/personnel-autocomplete.component';
import { MatFormField, MatLabel } from '@angular/material/form-field';
import { MatSelect } from '@angular/material/select';
import { MatOption } from '@angular/material/autocomplete';
@Component({
    selector: '',
    templateUrl: './list.component.html',
    styleUrls: ['./list.component.scss'],
    // encapsulation: ViewEncapsulation.None,
    // changeDetection: ChangeDetectionStrategy.OnPush,
    animations: fuseAnimations,
    providers: [
        { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMATS }, // ตั้งค่ารูปแบบวันที่ทั่วโปรเจกต์
        { provide: MAT_MOMENT_DATE_ADAPTER_OPTIONS, useValue: { useUtc: true } }, // ใช้ UTC หากต้องการ
    ],
    imports: [CommonModule, NgIf, MatProgressBar, FormsModule, ReactiveFormsModule, MatButton, MatIcon, PersonnelAutocompleteComponent, MatFormField, MatLabel, MatSelect, NgFor, MatOption]
})
export class ListComponent implements OnInit, AfterViewInit, OnDestroy {
    @ViewChild(DataTableDirective)
    dtElement!: DataTableDirective;
    public dtOptions: DataTables.Settings = {};
    public dataRow: any[];
    public UserList: any[] = [];
    public positions: any = [];
    formData: FormGroup;

    private destroy$ = new Subject<any>();
    formFieldHelpers: string[] = ['fuse-mat-dense'];
    flashMessage: 'success' | 'error' | null = null;
    isLoading: boolean = false;
    searchInputControl: FormControl = new FormControl();
    selectedProduct: any | null = null;
    filterForm: FormGroup;
    tagsEditMode: boolean = false;
    months = [
        { key: '01', name: 'มกราคม' },
        { key: '02', name: 'กุมภาพันธ์' },
        { key: '03', name: 'มีนาคม' },
        { key: '04', name: 'เมษายน' },
        { key: '05', name: 'พฤษภาคม' },
        { key: '06', name: 'มิถุนายน' },
        { key: '07', name: 'กรกฎาคม' },
        { key: '08', name: 'สิงหาคม' },
        { key: '09', name: 'กันยายน' },
        { key: '10', name: 'ตุลาคม' },
        { key: '11', name: 'พฤศจิกายน' },
        { key: '12', name: 'ธันวาคม' },
    ];
    years: number[] = [];
    itemData: any[] = [];

    private _unsubscribeAll: Subject<any> = new Subject<any>();
    /**
     * Constructor
     */
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseConfirmationService: FuseConfirmationService,
        private _formBuilder: FormBuilder,
        private _Service: WorkTimeMonthlyService,
        private _matDialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute
    ) {
        this.GetPosition()
        this.GetUserList()
        const currentYear = new Date().getFullYear(); // ดึงปีปัจจุบัน
        const startYear = currentYear - 5; // ปีเริ่มต้น
        const endYear = currentYear + 5; // ปีสิ้นสุด

        // สร้าง array ของปี
        for (let year = startYear; year <= endYear; year++) {
            this.years.push(year);
        }
        this.formData = this._formBuilder.group({
            year: currentYear,
            month: new Date().toLocaleString('en-US', { month: '2-digit' }),
            user_id: '',
            position_id: '',
            round: "1"
        });
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    onPersonnelSelected(selectedPersonnel: any): void {
        this.formData.patchValue({
            user_id: selectedPersonnel.id,
        })
    }
    ngOnInit(): void {


        // this.GetReport()
        this._changeDetectorRef.markForCheck()

    }
    hiddenEdit() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 26);
        return menu.edit == 0;
    }
    hiddenDelete() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 26);
        return menu.delete == 0;
    }
    hiddenSave() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 26);
        return menu.save == 0;
    }
    /**
     * After view init
     */
    ngAfterViewInit(): void { }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Close the details
     */

    /**
     * Show flash message
     */
    showFlashMessage(type: 'success' | 'error'): void {
        // Show the message
        this.flashMessage = type;

        // Mark for check
        this._changeDetectorRef.markForCheck();

        // Hide it after 3 seconds
        setTimeout(() => {
            this.flashMessage = null;

            // Mark for check
            this._changeDetectorRef.markForCheck();
        }, 3000);
    }

    Clear() {
        const currentYear = new Date().getFullYear(); // ดึงปีปัจจุบัน
        this.formData.patchValue({
            year: currentYear,
            month: new Date().toLocaleString('en-US', { month: '2-digit' }),
            user_id: '',
            position_id: ''
        })
    }

    GetPosition() {
        this._Service.getPosition().subscribe((resp: any) => {
            this.positions = resp.data
        })
    }
    GetUserList() {
        this._Service.getUser().subscribe((resp: any) => {
            this.UserList = resp.data
        })
    }
    GetReport() {
        let formValue = this.formData.value
        formValue.user_id = this.UserList.find(item => item.id === formValue.user_id)?.personnel_id || formValue.user_id;
        this._Service.getReport(formValue).subscribe((resp: any) => {
            this.itemData = resp.data
            this._changeDetectorRef.markForCheck()
        })
    }

    exportDailyReport() {
        let formValue = this.formData.value
        window.open(`${environment.API_URL}api/report_MonthlyReport?year=${formValue.year}&month=${formValue.month}&user_id=${formValue.user_id}&position_id=${formValue.position_id}&round=${formValue.round}`)
    }

    clearData() {
        this.formData.reset();
        this.formData.patchValue({
            year: new Date().getFullYear(),
            month: new Date().toLocaleString('en-US', { month: '2-digit' }),
            user_id: '',
            position_id: '',
            round: '1'
        })
        this.itemData = [];
        // console.log(this.formData.value);

    }
}
