<div class="sm:absolute sm:inset-0 flex flex-col flex-auto min-w-0 sm:overflow-auto bg-card dark:bg-transparent">
    <!-- Header -->
    <div
        class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b">
        <!-- Loader -->
        <div class="absolute inset-x-0 bottom-0" *ngIf="isLoading">
            <mat-progress-bar [mode]="'indeterminate'"></mat-progress-bar>
        </div>
        <!-- Title -->
        <div class="text-4xl font-extrabold tracking-tight">รายงานรายเดือน</div>
        <div class="flex gap-2">
            <div class="flex mt-0 sm:mt-6 md:mt-6 justify-end px-4 gap-2">
                <button mat-flat-button
                    class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
                    (click)="GetReport()">
                    <mat-icon [svgIcon]="'heroicons_outline:magnifying-glass'"></mat-icon>
                    <span class="ml-2 mr-1">ค้นหา</span>
                </button>
                <button mat-flat-button
                    class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
                    (click)="clearData()">
                    <mat-icon [svgIcon]="'heroicons_outline:arrow-path'"></mat-icon>
                    <span class="ml-2 mr-1">ล้าง</span>
                </button>
                <button mat-flat-button color="primary" class="rounded-lg py-2 px-4" (click)="exportDailyReport()">
                    <mat-icon [svgIcon]="'heroicons_outline:document'"></mat-icon>
                    <span class="ml-2 mr-1">PDF</span>
                </button>
            </div>
        </div>
        <!-- Actions -->
    </div>
    <!-- Main -->
    <form [formGroup]="formData">
        <div class="flex flex-col flex-auto min-w-0 bg-gray-100 dark:bg-transparent ">
            <div class="flex flex-col p-3 sm:p-2 bg-card">
                <div class="border-b flex flex-col md:flex-row gap-4 w-full p-4">
                    <div class="flex flex-col w-full">
                        <ng-container *ngIf="UserList.length > 0">
                            <mat-label for="name"
                                class="text-gray-800 text-lg font-bold leading-tight tracking-normal mb-1">เลือกพนักงาน</mat-label>
                            <app-personnel-autocomplete [itemtypeData]="UserList"
                                (personnelSelected)="onPersonnelSelected($event)">
                            </app-personnel-autocomplete>
                        </ng-container>
                    </div>
                    <div class="flex flex-col w-full">
                        <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
                            <mat-label for="name"
                                class="text-gray-800 text-lg font-bold leading-tight tracking-normal">เลือกปี</mat-label>
                            <mat-select [formControlName]="'year'">
                                <mat-option *ngFor="let item of years" [value]="item">
                                    {{item}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                    <div class="flex flex-col w-full">
                        <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
                            <mat-label for="name"
                                class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/3 mt-4">เลือกเดือน</mat-label>
                            <mat-select [formControlName]="'month'">
                                <mat-option *ngFor="let item of months" [value]="item.key">
                                    {{item.name}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                    <div class="flex flex-col w-full">
                        <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
                            <mat-label for="name"
                                class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/4 mt-4">เลือกรอบ</mat-label>
                            <mat-select formControlName="round">
                                <mat-option value="1">ครึ่งเดือนแรก </mat-option>
                                <mat-option value="2">ครึ่งเดือนหลัง</mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                    <div class="flex flex-col w-full">
                        <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
                            <mat-label for="position"
                                class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/3 mt-4">แผนก</mat-label>
                            <mat-select [formControlName]="'position_id'" placeholder="เลือกแผนก">
                                <mat-option *ngFor="let item of positions" [value]="item.id">
                                    {{item.name ?? '-'}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                </div>
                <div class="flex flex-col flex-auto p-2 sm:overflow-auto overflow-x-scroll">
                    <div class="overflow-x-auto shadow-md rounded-none">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-100 font-bold">
                                <tr>
                                    <th rowspan="2"
                                        class="px-4 py-3 text-center text-md font-medium text-gray-700 uppercase tracking-wider border">
                                        ลำดับ
                                    </th>
                                    <th rowspan="2"
                                        class="px-4 py-3 text-center text-md font-medium text-gray-700 uppercase tracking-wider border">
                                        รหัสพนักงาน
                                    </th>
                                    <th rowspan="2"
                                        class="px-4 py-3 text-center text-md font-medium text-gray-700 uppercase tracking-wider border">
                                        ชื่อพนักงาน
                                    </th>
                                    <th rowspan="2"
                                        class="px-4 py-3 text-center text-md font-medium text-gray-700 uppercase tracking-wider border">
                                        แผนก
                                    </th>
                                    <th colspan="2"
                                        class="px-4 py-3 text-center text-md font-medium text-gray-700 uppercase tracking-wider border">
                                        รวม
                                    </th>
                                    <th colspan="4"
                                        class="px-4 py-3 text-center text-md font-medium text-gray-700 uppercase tracking-wider border">
                                        ลา
                                    </th>
                                    <th rowspan="2"
                                        class="px-4 py-3 text-center text-md font-medium text-gray-700 uppercase tracking-wider border">
                                        % งาน
                                    </th>
                                    <th colspan="3"
                                        class="px-4 py-3 text-center text-md font-medium text-gray-700 uppercase tracking-wider border">
                                        ผลรวม
                                    </th>
                                </tr>
                                <tr>
                                    <th
                                        class="px-4 py-2 text-center text-md font-medium text-gray-700 uppercase tracking-wider border bg-slate-50">
                                        วันทำงาน
                                    </th>
                                    <th
                                        class="px-4 py-2 text-center text-md font-medium text-gray-700 uppercase tracking-wider border bg-slate-50">
                                        มาทำงาน
                                    </th>
                                    <th
                                        class="px-4 py-2 text-center text-md font-medium text-gray-700 uppercase tracking-wider border bg-slate-50">
                                        สาย
                                    </th>
                                    <th
                                        class="px-4 py-2 text-center text-md font-medium text-gray-700 uppercase tracking-wider border bg-slate-50">
                                        ขาด
                                    </th>
                                    <th
                                        class="px-4 py-2 text-center text-md font-medium text-gray-700 uppercase tracking-wider border bg-slate-50">
                                        ลากิจ
                                    </th>
                                    <th
                                        class="px-4 py-2 text-center text-md font-medium text-gray-700 uppercase tracking-wider border bg-slate-50">
                                        ลาป่วย
                                    </th>
                                    <th
                                        class="px-4 py-2 text-center text-md font-medium text-gray-700 uppercase tracking-wider border bg-slate-50">
                                        สาย
                                    </th>
                                    <th
                                        class="px-4 py-2 text-center text-md font-medium text-gray-700 uppercase tracking-wider border bg-slate-50">
                                        ออกก่อน
                                    </th>
                                    <th
                                        class="px-4 py-2 text-center text-md font-medium text-gray-700 uppercase tracking-wider border bg-slate-50">
                                        โอที
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr *ngFor="let item of itemData; let k = index" class="hover:bg-gray-50">
                                    <td class="px-4 py-3 whitespace-nowrap text-md text-gray-900 text-center border">
                                        {{k + 1}}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-md text-gray-900 text-center border">
                                        {{item?.user_id || '-'}}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-md text-gray-900 border">
                                        {{item?.name || '-'}}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-md text-gray-900 border">
                                        {{item?.position || '-'}}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-md text-gray-900 text-center border">
                                        {{item?.workday || '-'}}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-md text-gray-900 text-center border">
                                        {{item?.comework || '-'}}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-md text-orange-600 text-center border">
                                        {{item?.worklate || '-'}}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-md text-red-500 text-center border">
                                        {{item?.workmiss || '-'}}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-md text-gray-900 text-center border">
                                        {{item?.leaveBusiness || '-'}}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-md text-gray-900 text-center border">
                                        {{item?.leaveSick || '-'}}
                                    </td>
                                    <td
                                        class="px-4 py-3 whitespace-nowrap text-md text-gray-900 text-center font-medium border">
                                        {{item?.percentwork || '-'}}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-md text-gray-900 text-center border">
                                        {{item?.late_display || '-'}}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-md text-gray-900 text-center border">
                                        {{item?.out_time_display || '-'}}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-md text-gray-900 text-center border">
                                        {{item?.ot || '-'}}
                                    </td>
                                </tr>
                                <tr *ngIf="itemData.length === 0" class="hover:bg-gray-50">
                                    <td colspan="14" class="px-4 py-6 text-center text-md text-gray-500 border">
                                        ไม่มีข้อมูล
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>