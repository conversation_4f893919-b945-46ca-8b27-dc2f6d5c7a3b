import { HttpClient, HttpRequest, HttpHandler, HttpEvent, HttpHeaders, HttpInterceptor } from '@angular/common/http';
import { catchError } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { BehaviorSubject, filter, map, Observable, of, switchMap, take, tap, throwError } from 'rxjs';
import { environment } from 'environments/environment';
import { AssetCategory } from 'app/shared/asset-category';
import { DataTablesResponse } from 'app/shared/datatable.types';
import { round } from 'lodash';
// import { UserDetail } from '../user/user.types';
const token = localStorage.getItem('accessToken') || null;
@Injectable({
  providedIn: 'root'
})
export class WorkTimeMonthlyService {
  branchId: string;
  // Private
  private _materials: BehaviorSubject<any | null> = new BehaviorSubject(null);
  //
  /**
   * Constructor
   */
  constructor(private _httpClient: HttpClient) {
    const user = JSON.parse(localStorage.getItem('user'))
    this.branchId = user.branch_id
  }

  httpOptionsFormdata = {
    headers: new HttpHeaders({ Authorization: `Bearer ${token}` })
  };

  getPosition(): Observable<any[]> {
    return this._httpClient
      .get<any[]>(environment.API_URL + 'api/get_position')
      .pipe(
        tap((meterial) => {
          this._materials.next(meterial);
        })
      );
  }
  getUser(): Observable<any[]> {
    return this._httpClient
      .get<any[]>(environment.API_URL + 'api/get_user')
      .pipe(
        tap((meterial) => {
          this._materials.next(meterial);
        })
      );
  }

  getReport(data: any): Observable<any[]> {
    return this._httpClient
      .get<any[]>(environment.API_URL + 'api/report_MonthlyReportJSON', {
        params: {
            user_id: data.user_id,
            year: data.year,
            month: data.month,
            position_id: data.position_id,
            round: data.round,
            branch_id: this.branchId
        }
      })
      .pipe(
        tap((meterial) => {
          this._materials.next(meterial);
        })
      );
  }
}
