import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnD<PERSON>roy, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { debounceTime, map, merge, Observable, Subject, switchMap, takeUntil } from 'rxjs';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from 'environments/environment';
import { AuthService } from 'app/core/auth/auth.service';
import { sortBy, startCase } from 'lodash-es';
import { AssetType, BranchPagination } from '../ot.types';
import { ItemTypeService } from '../ot.service';
import { MatFormField } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { MatSelect } from '@angular/material/select';
import { MatOption } from '@angular/material/autocomplete';
import { NgFor, NgIf } from '@angular/common';
import { MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
// import { ImportOSMComponent } from '../card/import-osm/import-osm.component';

@Component({
    selector: 'edit-item-type',
    templateUrl: './edit-item-type.component.html',
    styleUrls: ['./edit-item-type.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush
    // animations: fuseAnimations
    ,
    imports: [FormsModule, ReactiveFormsModule, MatFormField, MatInput, MatSelect, MatOption, NgFor, NgIf, MatButton, MatIcon]
})

export class EditItemTypeComponent implements OnInit, AfterViewInit, OnDestroy {
    itemtypeData: any;
    statusData = [
        { id: 0, name: 'ปิดการใช้งาน' },
        { id: 1, name: 'เปิดการใช้งาน' },
    ];
    itemtypeId: string;
    formData: FormGroup
    flashErrorMessage: string;
    flashMessage: 'success' | 'error' | null = null;
    isLoading: boolean = false;
    OtType:any = [];
    statusAC:any = [
        {name:'อนุมัติโอที',value:'approved'},
        {name:'หัวหน้าไม่อนุมัติ',value:'head_cencal'},
        {name:'ยกเลิกโอที',value:'cancel'}
    ];

    /**
     * Constructor
     */
    constructor(
        public dialogRef: MatDialogRef<EditItemTypeComponent>,
        @Inject(MAT_DIALOG_DATA) private _data,
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseConfirmationService: FuseConfirmationService,
        private _formBuilder: FormBuilder,
        private _Service: ItemTypeService,
        private _matDialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _authService: AuthService,
    ) {

        this.formData = this._formBuilder.group({
            qty_hour: '',
            status: ['', Validators.required],
            remark: ['',Validators.required],

        })

    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    othour: any;
    calculateOvertime() {
        const timeStart = this._data.time_start;
        const timeEnd = this._data.time_end;

        // แปลง time_start และ time_end เป็น Date objects
        const start = this.convertToDate(timeStart);
        const end = this.convertToDate(timeEnd);

        // คำนวณช่วงเวลาระหว่าง time_start และ time_end
        const overtimeInMillis = end.getTime() - start.getTime(); // คำนวณต่างระหว่างสองเวลาเป็นมิลลิวินาที

        // แปลงมิลลิวินาทีเป็นชั่วโมงและนาที
        const overtimeHours = Math.floor(overtimeInMillis / (1000 * 60 * 60)); // แปลงมิลลิวินาทีเป็นชั่วโมง
        const overtimeMinutes = Math.floor((overtimeInMillis % (1000 * 60 * 60)) / (1000 * 60)); // แปลงมิลลิวินาทีที่เหลือเป็นนาที

        // คำนวณเวลาโอทีในรูปแบบทศนิยม
        const overtimeDecimal = overtimeHours + overtimeMinutes / 60;

        // ใช้ Math.abs() เพื่อให้ค่าที่ได้ไม่เป็นลบ
        this.othour = Math.abs(overtimeDecimal);

      }

      // ฟังก์ชันแปลงเวลาจาก 'HH:mm' เป็น Date object
      convertToDate(time: string): Date {
        const [hours, minutes] = time.split(':').map(Number);
        const date = new Date();
        date.setHours(hours);
        date.setMinutes(minutes);
        date.setSeconds(0);
        return date;
      }


    ngOnInit(): void {
        this.getTypeOt();
        this.itemtypeId = this._activatedRoute.snapshot.paramMap.get('id');
        this._Service.getOtById(this._data.itemid).subscribe((resp: any) => {
            this._Service.getOthour(this._data.itemid).subscribe((data: any) => {
            this.itemtypeData = resp.data
            this.formData.patchValue({
                ...resp.data,

                status: this._data.status,
                qty_hour: data.data,
            })
        })

            const startDate = new Date(resp.data.time_start).getMilliseconds();
            const endDate = new Date(resp.data.time_end).getMilliseconds();



        // })


        })
    }

    /**
     * After view init
     */
    ngAfterViewInit(): void {

    }

    onClose(): void {
        this.dialogRef.close();
    }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions

    }


    approve(): void {
        this.flashMessage = null;
        this.flashErrorMessage = null;
        // Return if the form is invalid
        // if (this.formData.invalid) {
        //     return;
        // }
        // Open the confirmation dialog
        const confirmation = this._fuseConfirmationService.open({
            "title": "แก้ไขโอที",
            "message": "คุณต้องการแก้ไขโอทีใช่หรือไม่ ",
            "icon": {
                "show": false,
                "name": "heroicons_outline:exclamation-triangle",
                "color": "warning"
            },
            "actions": {
                "confirm": {
                    "show": true,
                    "label": "ยืนยัน",
                    "color": "primary"
                },
                "cancel": {
                    "show": true,
                    "label": "ยกเลิก"
                }
            },
            "dismissible": true
        });

        // Subscribe to the confirmation dialog closed action
        confirmation.afterClosed().subscribe((result) => {
            let Id = this._data.itemid
            // If the confirm button pressed...
            if (result === 'confirmed') {
                this._Service.approve(this.formData.value,Id).subscribe({
                    next: (resp: any) => {
                        this.dialogRef.close();
                    },
                    error: (err: any) => {

                        this._fuseConfirmationService.open({
                            "title": "เกิดข้อผิดพลาด",
                            "message": err.error.message,
                            "icon": {
                                "show": true,
                                "name": "heroicons_outline:exclamation-triangle",
                                "color": "warning"
                            },
                            "actions": {
                                "confirm": {
                                    "show": false,
                                    "label": "ยืนยัน",
                                    "color": "primary"
                                },
                                "cancel": {
                                    "show": false,
                                    "label": "ยกเลิก",

                                }
                            },
                            "dismissible": true
                        });
                    }
                });

            }
        });

    }

    showFlashMessage(type: 'success' | 'error'): void {
        // Show the message
        this.flashMessage = type;

        // Mark for check
        this._changeDetectorRef.markForCheck();

        // Hide it after 3 seconds
        setTimeout(() => {

            this.flashMessage = null;

            // Mark for check
            this._changeDetectorRef.markForCheck();
        }, 3000);
    }

    getTypeOt(){
        this._Service.getTypeOt().subscribe((res:any)=>{

           this.OtType = res.data


       })
   }



}
