<div
    class="flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between p-1 sm:py-8 sm:px-10 border-b bg-card dark:bg-transparent">
    <div class="flex-1 min-w-0">
        <!-- Title -->
        <div class="mt-2">
            <h1 class="text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
                อนุมัติโอที
            </h1>
        </div>
    </div>
</div>
<div class="flex-auto p-3 sm:p-2">
    <form [formGroup]="formData">
        <div class="flex p-5">
            <label for="name"
                class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/3 mt-4">จำนวนชั่วโมง</label>
                <mat-form-field class="w-full">
                    <input matInput [formControlName]="'qty_hour'" placeholder="กรุณาระบุชั่วโมง" type="text">
                </mat-form-field>
        </div>
        <div class="bg-white px-4 py-5 flex sm:px-6">
            <dt class=" font-extrabold   mt-4 text-l w-1/3 text-xl">สถานะ</dt>
            <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0 w-full">
                <mat-form-field class="w-full pr-2" >
                    <mat-select formControlName="status" >
                       <mat-option *ngFor="let item of statusAC" [value]="item.value">
                           {{item.name}}
                       </mat-option>
                   </mat-select>
               </mat-form-field>
            </dd>
        </div>
        <div *ngIf="formData.value.status === 'cancel' || formData.value.status === 'head_cancel'" class="flex p-5">
            <label for="name"
                class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/3 mt-4">รายละเอียด</label>
            <textarea  id="name" rows="4"  class=" p-2.5 mb-5 mt-2 text-gray-600 focus:outline-none focus:border focus:border-yellow-600 font-normal w-full  flex items-center pl-3 text-sm border-gray-300 rounded border" placeholder="รายละเอียด" formControlName="remark" ></textarea>
        </div>
        <div class="flex items-center justify-end w-full border-t px-8 py-4">
            <div class="flex items-center justify-end">
                <button mat-flat-button class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4" (click)="onClose()">
                    <mat-icon svgIcon="heroicons_solid:x-mark" class="mr-2"></mat-icon>
                    ยกเลิก
                </button>
                <button class="px-6 ml-3 mat-primary" mat-flat-button (click)="approve()">
                    <mat-icon svgIcon="heroicons_solid:check"></mat-icon>
                    ยืนยัน
                </button>
            </div>
        </div>
    </form>
</div>
