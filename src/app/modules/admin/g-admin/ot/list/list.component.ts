import {
    After<PERSON>iewInit,
    ChangeDetector<PERSON><PERSON>,
    Component,
    On<PERSON><PERSON>roy,
    OnInit,
    ViewChild,
} from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import {
    Subject,
    takeUntil,
} from 'rxjs';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
// import { ImportOSMComponent } from '../card/import-osm/import-osm.component';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { ItemTypeService } from '../ot.service';
import { EditItemTypeComponent } from '../edit-item-type/edit-item-type.component';
import { NewItemTypeComponent } from '../new-item-type/new-item-type.component';
import moment from 'moment';
import { TimeAttendanceService } from '../../report/time-attendance/time-attendance.service';
import { CommonModule } from '@angular/common';
import { PersonnelAutocompleteComponent } from 'app/shared/personnel-autocomplete/personnel-autocomplete.component';
import { sharedImports } from 'app/shared-imports';
import { MatDatepickerModule } from "@angular/material/datepicker";
import { BuddhistDatePipe } from 'app/modules/admin/pipes/buddhist-date.pipe';
@Component({
    selector: '',
    templateUrl: './list.component.html',
    styleUrls: ['./list.component.scss'],
    animations: fuseAnimations,
    standalone: true,
    imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    DataTablesModule,
    ...sharedImports,
    PersonnelAutocompleteComponent,
    BuddhistDatePipe

    
]
})
export class ListComponent implements OnInit, AfterViewInit, OnDestroy {
    @ViewChild(DataTableDirective)
    dtElement!: DataTableDirective;
    public dtOptions: DataTables.Settings = {};
    public dataRow: any[];
    private destroy$ = new Subject<any>();
    // dataRow: any = []
    @ViewChild(MatPaginator) _paginator: MatPaginator;
    @ViewChild(MatSort) private _sort: MatSort;

    flashMessage: 'success' | 'error' | null = null;
    isLoading: boolean = false;
    searchInputControl: FormControl = new FormControl();
    selectedProduct: any | null = null;
    filterForm: FormGroup;
    tagsEditMode: boolean = false;
    formFieldHelpers: string[] = ['fuse-mat-dense'];
    status: any[] = [
        {
            value: '',
            name: 'ทั้งหมด'
        },
        {
            value: 'open',
            name: 'รอหัวหน้าอนุมัติ'
        },
        {
            value: 'process',
            name: 'รอ HR อนุมัติ'
        },
        {
            value: 'approved',
            name: 'อนุมัติ'
        },
        {
            value: 'approved',
            name: 'ไม่อนุมัติ'
        },
    ]
    itemtypeData: any[];
    private _unsubscribeAll: Subject<any> = new Subject<any>();
    /**
     * Constructor
     */
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseConfirmationService: FuseConfirmationService,
        private _formBuilder: FormBuilder,
        private _Service: ItemTypeService,
        private _ServiceUser: TimeAttendanceService,
        private _matDialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute
    ) {
        const currentDate = new Date()
        this.filterForm = this._formBuilder.group({
            date: null,
            date_start: null,
            date_end: null,
            status: null,
            user_id: null,
            created_at: null
        })

    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    async ngOnInit(): Promise<void> {
        this.loadTable();
        this.GetUser()

    }
    hiddenEdit() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 28);
        return menu.edit == 0;
    }
    hiddenDelete() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 28);
        return menu.delete == 0;
    }
    hiddenSave() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 28);
        return menu.save == 0;
    }
    /**
     * After view init
     */
    ngAfterViewInit(): void { }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Close the details
     */

    /**
     * Show flash message
     */
    showFlashMessage(type: 'success' | 'error'): void {
        // Show the message
        this.flashMessage = type;

        // Mark for check
        this._changeDetectorRef.markForCheck();

        // Hide it after 3 seconds
        setTimeout(() => {
            this.flashMessage = null;

            // Mark for check
            this._changeDetectorRef.markForCheck();
        }, 3000);
    }

    changeStatus(data: any) {
        // alert(data)
        this.filterForm.patchValue({
            status: data
        })
        this.rerender()

    }

    UpdateStatus(item: any, status: string) {
        const dialogRef = this._matDialog.open(EditItemTypeComponent, {
            width: '500px',
            height: 'auto',
            data: {
                itemid: item.id,
                time_start: item.time_start,
                time_end: item.time_end,
                status: status
            },
        });
        dialogRef.afterClosed().subscribe((item) => {
            this.rerender();
            this._changeDetectorRef.markForCheck();
        });
    }
    New() {
        const dialogRef = this._matDialog.open(NewItemTypeComponent, {
            maxWidth: '90vw',
            width: '500px',
            height: '600px',
        });

        dialogRef.afterClosed().subscribe((item) => {
            this.rerender();
            this._changeDetectorRef.markForCheck();
        });
    }

    GetUser() {
        this._ServiceUser.getUser('').subscribe((resp: any) => {
            this.itemtypeData = resp.data

        })
    }

    pages = { current_page: 1, last_page: 1, per_page: 10, begin: 0 };
    loadTable(): void {
        let formValue = this.filterForm.value;
        if (formValue.date) {
            formValue.date = moment(formValue.date).format('YYYY-MM-DD')
        } else {
            formValue.date = null
        }

        if (formValue.date_start) {
            formValue.date_start = moment(formValue.date_start).format('YYYY-MM-DD')
        } else {
            formValue.date_start = null
        }

        if (formValue.date_end) {
            formValue.date_end = moment(formValue.date_end).format('YYYY-MM-DD')
        } else {
            formValue.date_end = null
        }

        const that = this;
        this.dtOptions = {
            pagingType: 'full_numbers',
            pageLength: 10,
            serverSide: true,
            processing: true,
            order: [0, "desc"],
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.11.3/i18n/th.json',
            },
            ajax: (dataTablesParameters: any, callback) => {
                dataTablesParameters.status = this.filterForm.value.status
                dataTablesParameters.date = formValue.date
                dataTablesParameters.user_id = formValue.user_id
                dataTablesParameters.date_start = formValue.date_start
                dataTablesParameters.date_end = formValue.date_end
                that._Service
                    .getOtPage(dataTablesParameters)
                    .subscribe((resp) => {
                        this.dataRow = resp.data;
                        this.pages.current_page = resp.current_page;
                        this.pages.last_page = resp.last_page;
                        this.pages.per_page = resp.per_page;
                        if (resp.current_page > 1) {
                            this.pages.begin =
                                resp.per_page * resp.current_page - 1;
                        } else {
                            this.pages.begin = 0;
                        }
                        callback({
                            recordsTotal: resp.total,
                            recordsFiltered: resp.total,
                            data: [],
                        });
                        this._changeDetectorRef.markForCheck();
                    });
            },
            columns: [
                // { data: 'actice', orderable: false },
                { data: 'date' },
                { data: 'code' },
                { data: 'name' },
                { data: 'ot_type_id' },
                { data: 'time_start' },
                { data: 'time_end' },
                { data: 'qty' },
                { data: 'status' },
                { data: 'remark' },
            ],
        };
    }

    rerender(): void {
        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
            dtInstance.ajax.reload();
        });
    }

    Delete(id) {
        const confirmation = this._fuseConfirmationService.open({
            title: 'บันทึก',
            message: 'คุณต้องการลบโอทีใช่หรือไม่ ',
            icon: {
                show: false,
                name: 'heroicons_outline:exclamation-triangle',
                color: 'warning',
            },
            actions: {
                confirm: {
                    show: true,
                    label: 'ยืนยัน',
                    color: 'danger',
                },
                cancel: {
                    show: true,
                    label: 'ยกเลิก',
                },
            },
            dismissible: true,
        });

        // Subscribe to the confirmation dialog closed action
        confirmation.afterClosed().subscribe((result) => {
            // If the confirm button pressed...
            if (result === 'confirmed') {
                this._Service
                    .delete(id)
                    .pipe(takeUntil(this.destroy$))
                    .subscribe((res: any) => {
                        if (res.code == 201) {
                            this._fuseConfirmationService
                                .open({
                                    title: 'ลบข้อมูลโอที',
                                    message: 'บันทึกเรียบร้อย',
                                    icon: {
                                        show: true,
                                        name: 'heroicons_outline:check-circle',
                                        color: 'success',
                                    },
                                    actions: {
                                        confirm: {
                                            show: false,
                                            label: 'ตกลง',
                                            color: 'primary',
                                        },
                                        cancel: {
                                            show: false,
                                            label: 'ยกเลิก',
                                        },
                                    },
                                    dismissible: true,
                                })
                                .afterClosed()
                                .subscribe((res) => {
                                    this.rerender();
                                });
                        }
                    });
            }
        });
    }

    onPersonnelSelected(selectedPersonnel: any): void {
        this.filterForm.patchValue({
            user_id: selectedPersonnel.id,
        })
    }
}


