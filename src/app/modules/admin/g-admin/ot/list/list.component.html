<div class="sm:absolute sm:inset-0 flex flex-col flex-auto min-w-0 sm:overflow-auto bg-card dark:bg-transparent">

    <!-- Header -->
    <div
        class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b">
        <!-- Loader -->
        <div class="absolute inset-x-0 bottom-0" *ngIf="isLoading">
            <mat-progress-bar [mode]="'indeterminate'"></mat-progress-bar>
        </div>
        <!-- Title -->
        <div class="text-4xl font-extrabold tracking-tight">โอที</div>
        <!-- Actions -->


        <!-- <div class="flex shrink-0 items-center mt-6 sm:mt-0 sm:ml-4">
            <a (click)="New()" class="ml-4" mat-flat-button [color]="'primary'" *ngIf="!hiddenSave()">
                <mat-icon [svgIcon]="'heroicons_outline:plus'"></mat-icon>
                <span class="ml-2 mr-1">เพิ่มโอที</span>
            </a>
        </div> -->
    </div>
    <!-- Main -->
    <div class="flex flex-col flex-auto min-w-0 bg-gray-100 dark:bg-transparent">

        <div class="flex flex-col p-3 sm:p-6">
            <div class="overflow-auto  bg-white shadow sm:rounded-lg">
                <div class="flex flex-col md:flex-row justify-between py-2 px-5">
                    <form [formGroup]="filterForm" class="flex flex-col md:flex-row w-full md:w-2/3">
                        <div class="flex flex-col md:flex-row justify-start items-center w-full mt-5 gap-2">

                            <div
                                class="flex text-md font-normal tracking-tight leading-7 sm:leading-10 truncate justify-center items-center w-full">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>วันที่ขอ</mat-label>
                                    <input matInput [matDatepicker]="pickDate" [formControlName]="'date'"
                                        (dateChange)="this.rerender()">
                                    <mat-datepicker-toggle matSuffix [for]="pickDate"></mat-datepicker-toggle>
                                    <mat-datepicker #pickDate></mat-datepicker>
                                </mat-form-field>
                            </div>
                            <div
                                class="flex text-md font-normal tracking-tight leading-7 sm:leading-10 truncate justify-center items-center w-full">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>วันที่ทำโอที</mat-label>
                                    <mat-date-range-input [rangePicker]="MakeDate">
                                        <input matStartDate formControlName="date_start" placeholder="วันที่เริ่มต้น">
                                        <input matEndDate formControlName="date_end" placeholder="วันที่สิ้นสุด">
                                    </mat-date-range-input>
                                    <mat-datepicker-toggle matIconSuffix [for]="MakeDate"></mat-datepicker-toggle>
                                    <mat-date-range-picker #MakeDate></mat-date-range-picker>
                                </mat-form-field>
                            </div>
                            <div
                                class="flex text-md font-normal tracking-tight leading-7 sm:leading-10 truncate justify-center items-center w-full">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>สถานะ</mat-label>
                                    <mat-select [formControlName]="'status'" placeholder="เลือกสถานะ"
                                        (selectionChange)="changeStatus($event.value)">
                                        <mat-option *ngFor="let item of status" [value]="item.value">
                                            {{item.name}}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </div>
                            <div class="flex flex-col w-full">
                                <ng-container *ngIf="itemtypeData">
                                    <label class="font-semibold">เลือกพนักงาน</label>
                                    <app-personnel-autocomplete [itemtypeData]="itemtypeData"
                                        (personnelSelected)="onPersonnelSelected($event)"
                                        (selectionChange)="rerender()">
                                    </app-personnel-autocomplete>
                                </ng-container>
                            </div>
                            <!-- <div
                                class="flex text-md font-normal tracking-tight leading-7 sm:leading-10 truncate justify-center items-center w-full">
                                <div class="w-full flex flex-col">
                                    <mat-label class="font-semibold">เลือกพนักงาน</mat-label>
                                    <ng-container *ngIf="itemtypeData.length > 0">
                                        <app-personnel-autocomplete [itemtypeData]="itemtypeData"
                                            (personnelSelected)="onPersonnelSelected($event)">
                                        </app-personnel-autocomplete>
                                    </ng-container>
                                </div>
                            </div> -->
                        </div>
                    </form>
                </div>
                <div class="flex flex-col flex-auto p-5  sm:overflow-y-auto">
                    <table datatable [dtOptions]="dtOptions"
                        class="table w-full text-left text-gray-500 overflow-hidden">
                        <thead class="bg-gray-300 text-black">
                            <tr>
                                <th class="whitespace-nowrap">จัดการ</th>
                                <th class="whitespace-nowrap">ลำดับ</th>
                                <th class="whitespace-nowrap">วันที่ขอ</th>
                                <th class="whitespace-nowrap">รหัสพนักงาน</th>
                                <th class="whitespace-nowrap">ชื่อ - นามสกุล</th>
                                <th class="whitespace-nowrap">วันที่</th>
                                <th class="whitespace-nowrap"> ประเภทโอที </th>
                                <th class="whitespace-nowrap">เวลาเริ่ม</th>
                                <th class="whitespace-nowrap">เวลาสิ้นสุด</th>
                                <th class="whitespace-nowrap">จำนวนเงิน</th>
                                <th class="whitespace-nowrap items-center">สถานะ</th>
                                <th class="whitespace-nowrap">หมายเหตุ</th>
                            </tr>
                        </thead>
                        <tbody *ngIf="dataRow?.length != 0">
                            <tr *ngFor="let item of dataRow; let i = index"
                                class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 text-md">
                                <td class="text-center">
                                    <!-- ปุ่มเปิดเมนู -->
                                    <button mat-icon-button [matMenuTriggerFor]="actionMenu" [disabled]="hiddenEdit()"
                                        matTooltip="ตัวเลือก">
                                        <mat-icon>fact_check</mat-icon>
                                    </button>

                                    <!-- เมนูตัวเลือก -->
                                    <mat-menu #actionMenu="matMenu">
                                        <button mat-menu-item (click)="UpdateStatus(item, 'approved')"
                                            *ngIf="item.status === 'process' || item.status === 'open'">
                                            <mat-icon class="text-green-500">check_circle</mat-icon>
                                            <span>อนุมัติ</span>
                                        </button>
                                        <button mat-menu-item (click)="UpdateStatus(item, 'head_cencal')"
                                            *ngIf="item.status === 'process' || item.status === 'open'">
                                            <mat-icon class="text-red-500">cancel</mat-icon>
                                            <span>ไม่อนุมัติ</span>
                                        </button>
                                        <button mat-menu-item (click)="UpdateStatus(item, 'cancel')">
                                            <mat-icon class="text-slate-500">cancel</mat-icon>
                                            <span>ยกเลิก</span>
                                        </button>
                                    </mat-menu>
                                </td>
                                <td style="min-width: 80px;" class="whitespace-nowrap">{{ pages.begin + (i + 1) }}</td>
                                <td class="whitespace-nowrap"> {{ item.created_at | buddhistDate: 'dd/MM/yyyy HH:mm' }}
                                </td>
                                <td class="whitespace-nowrap"> {{item.user ? item.user?.user_id: '-'}} </td>
                                <td class="whitespace-nowrap"> {{item.user ? item.user?.first_name: '-'}} {{item.user ?
                                    item.user?.last_name:
                                    '-'}} </td>
                                <td class="whitespace-nowrap"> {{item.date | buddhistDate : 'dd/MM/yyyy'}} </td>
                                <td class="whitespace-nowrap"> {{item.ot_type.name}} </td>
                                <td class="whitespace-nowrap"> {{item.time_start}} </td>
                                <td class="whitespace-nowrap"> {{item.time_end}} </td>
                                <td class="whitespace-nowrap"> {{item.qty | number:'1.2'}} </td>
                                <td class="text-center">
                                    <div class=" text-blue-500 text-md font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-green-200 dark:text-green-900"
                                        *ngIf="item.status === 'open'">
                                        รอหัวหน้าอนุมัติ
                                    </div>
                                    <div class=" text-green-500 text-md font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-green-200 dark:text-green-900"
                                        *ngIf="item.status === 'process'">
                                        หัวหน้าอนุมัติ
                                    </div>
                                    <div class=" text-green-500 text-md font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-green-200 dark:text-green-900"
                                        *ngIf="item.status === 'approved'">
                                        อนุมัติ
                                    </div>
                                    <div class=" text-red-500 text-md font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-green-200 dark:text-green-900"
                                        *ngIf="item.status === 'cancel'">
                                        ยกเลิก
                                    </div>
                                    <div class=" text-red-500 text-md font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-green-200 dark:text-green-900"
                                        *ngIf="item.status === 'head_cancel'">
                                        หัวหน้าไม่อนุมัติ
                                    </div>
                                </td>
                                <td class="whitespace-nowrap"> {{item.remark ? item.remark:'-' }}
                                </td>
                            </tr>
                        </tbody>
                        <tbody *ngIf="dataRow?.length == 0">
                            <tr>
                                <td colspan="11" class="no-data-available text-center">ไม่มีข้อมูล !</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>