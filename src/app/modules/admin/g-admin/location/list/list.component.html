<div class="sm:absolute sm:inset-0 flex flex-col flex-auto min-w-0 sm:overflow-auto bg-card dark:bg-transparent">

    <!-- Header -->
    <div
        class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b">
        <!-- Loader -->
        <div class="absolute inset-x-0 bottom-0" *ngIf="isLoading">
            <mat-progress-bar [mode]="'indeterminate'"></mat-progress-bar>
        </div>
        <!-- Title -->
        <div class="text-4xl font-extrabold tracking-tight">รายการสถานที่</div>
        <!-- Actions -->
        <div *ngIf="roleType == 'marketing'" class="flex shrink-0 items-center mt-6 sm:mt-0 sm:ml-4">

            <!-- Add product button -->


        </div>
    </div>

    <!-- Main -->
    <div class="flex flex-col flex-auto min-w-0 bg-gray-100 dark:bg-transparent">

        <div class="flex flex-col p-3 sm:p-6">
            <div class="overflow-auto  bg-white shadow sm:rounded-lg">
                <!-- Products list -->
                <div class="flex flex-row justify-between py-2 px-5">
                    <div class="flex justify-start items-center w-[405px] mt-5">
                        <div
                            class="flex text-md font-normal tracking-tight leading-7 sm:leading-10 truncate justify-center items-center">
                            <mat-form-field [ngClass]="formFieldHelpers" class="min-w-[405px] min-h-[40px]">
                                <mat-icon svgIcon="search"></mat-icon>
                                <input matInput placeholder="ค้นหา" [(ngModel)]="searchQuery"
                                    (ngModelChange)="applySearch()">
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="flex">
                        <div *ngIf="!hiddenSave()"
                            class="flex text-md font-normal tracking-tight leading-7 sm:leading-10 truncate justify-center items-center">
                            <div class="flex items-center mt-6 sm:mt-0"></div>
                            <a (click)="New()" class="ml-4" mat-flat-button [color]="'primary'" *ngIf="!hiddenSave()">
                                <mat-icon [svgIcon]="'heroicons_outline:plus'"></mat-icon>
                                <span class="ml-2 mr-1">เพิ่มสถานที่ใหม่</span>
                            </a>

                        </div>
                    </div>
                </div>
                <div class="flex flex-col flex-auto p-5 sm:overflow-auto overflow-x-scroll">


                    <table datatable [dtOptions]="dtOptions"
                        class="table w-full text-left text-gray-500 overflow-hidden">
                        <thead class="bg-gray-300 text-black">
                            <tr>

                                <th class="text-center ">จัดการ</th>
                                <th class="text-center ">ลำดับ</th>
                                <th class="text-center">ชื่อสถานที่</th>
                                <th class="text-center">ละติจูด</th>
                                <th class="text-center">ลองติจูด</th>
                                <th class="text-center">รัศมี (เมตร)</th>

                            </tr>
                        </thead>
                        <tbody *ngIf="dataRow?.length != 0" class="divide-y divide-gray-200">
                            <tr *ngFor="let item of dataRow; let i = index"
                                class="bg-white hover:bg-gray-200 transition-colors duration-300 ease-in-out">
                                <td class="text-center whitespace-nowrap">
                                    <button mat-icon-button (click)="Edit(item.id)" title="แก้ไขข้อมูล"
                                        [disabled]="hiddenEdit()">
                                        <mat-icon>edit</mat-icon>
                                    </button>
                                    <button mat-icon-button class="delete-button" (click)="Delete(item.id)"
                                        title="ลบข้อมูล" [disabled]="hiddenDelete()">
                                        <mat-icon class="delete-icon">delete</mat-icon>
                                    </button>
                                </td>
                                <td class="text-center whitespace-nowrap">{{ pages.begin + (i + 1) }}</td>
                                <td class="text-center whitespace-nowrap">{{ item.name }}</td>
                                <td class="text-center whitespace-nowrap">
                                    {{ item.latitude ?? '-' }}
                                </td>
                                <td class="text-center whitespace-nowrap">
                                    {{ item.longitude ?? '-' }}
                                </td>
                                <td class="text-center whitespace-nowrap">
                                    {{ item.radius ?? '-' }}
                                </td>
                            </tr>
                        </tbody>
                        <tbody *ngIf="dataRow?.length == 0">
                            <tr>
                                <td colspan="6" class="no-data-available text-center">ไม่มีข้อมูล !</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

    </div>

</div>