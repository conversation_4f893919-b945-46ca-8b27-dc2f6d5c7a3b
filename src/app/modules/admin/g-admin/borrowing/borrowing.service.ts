import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { DataTablesResponse } from 'app/shared/datatable.types';
import { environment } from 'environments/environment';
import { Observable, of, switchMap } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class BorrowingService {

    private _http = inject(HttpClient);

    constructor() { }

    getDataPage(
        dataTablesParameters: any
    ): Observable<DataTablesResponse> {
        return this._http
            .post(
                environment.API_URL + 'api/equipment/borrowings/page',
                dataTablesParameters
            )
            .pipe(
                switchMap((response: any) => {
                    return of(response);
                })
            );
    }
}
