import { After<PERSON>iewInit, ChangeDetectorRef, Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { FormControl, FormGroup, FormBuilder, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { Router, ActivatedRoute } from '@angular/router';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { AuthService } from 'app/core/auth/auth.service';
import { AssetType } from 'app/shared/asset-category';
import { environment } from 'environments/environment';
import moment from 'moment';
import { Subject, Observable, takeUntil } from 'rxjs';
import { DataWarehouse } from '../../advancemoney/advancemoney.types';
import { EditComponent } from '../../advancemoney/edit/edit.component';
import { NewComponent } from '../../advancemoney/new/new.component';
import { DialogBorrowingFormComponent } from '../dialog-borrowing-form/dialog-borrowing-form.component';
import { BranchPagination } from '../../branch/branch.types';
import { LeaveService } from '../../leave-list/leave-list.service';
import { TimeAttendanceService } from '../../time-attendance/time-attendance.service';
import { BorrowingService } from '../borrowing.service';
import { CommonModule } from '@angular/common';
import { BuddhistDatePipe } from 'app/modules/admin/pipes/buddhist-date.pipe';
import { sharedImports } from 'app/shared-imports';
import { PersonnelAutocompleteComponent } from 'app/shared/personnel-autocomplete/personnel-autocomplete.component';

@Component({
    selector: 'app-borrowing-list',
    imports: [
        DataTablesModule,
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        ...sharedImports,
        PersonnelAutocompleteComponent,
        BuddhistDatePipe
    ],
    templateUrl: './borrowing-list.component.html',
    styleUrl: './borrowing-list.component.scss',
    standalone: true,
})
export class BorrowingListComponent implements OnInit, AfterViewInit, OnDestroy {

    @ViewChild(DataTableDirective)
    dtElement!: DataTableDirective;
    dtOptions: DataTables.Settings = {};

    dataRow: any[] = [];
    @ViewChild(MatPaginator) _paginator: MatPaginator;
    @ViewChild(MatSort) private _sort: MatSort;

    displayedColumns: string[] = [
        'id',
        'name',
        'code',
        'status',
        'create_by',
        'created_at',
        'actions',
    ];
    dataSource: MatTableDataSource<DataWarehouse>;

    products$: Observable<any>;
    asset_types: AssetType[];
    flashMessage: 'success' | 'error' | null = null;
    isLoading: boolean = false;
    searchInputControl: FormControl = new FormControl();
    selectedProduct: any | null = null;
    filterForm: FormGroup;
    tagsEditMode: boolean = false;
    private _unsubscribeAll: Subject<any> = new Subject<any>();
    env_path = environment.API_URL;

    supplierId: string | null;
    pagination: BranchPagination;
    formFieldHelpers: string[] = ['fuse-mat-dense'];
    status: { value: string, name: string }[] = [
        { value: null, name: 'ทั้งหมด' },
        { value: 'pending', name: 'รออนุมัติ' },
        { value: 'approved', name: 'อนุมัติ' },
        { value: 'borrowed', name: 'ยืมแล้ว' },
        { value: 'returned', name: 'คืนแล้ว' },
        { value: 'cancelled', name: 'ยกเลิก' },
        { value: 'overdue', name: 'เลยกำหนด' },
    ];

    itemtypeData: any[];
    /**
     * Constructor
     */
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseConfirmationService: FuseConfirmationService,
        private _formBuilder: FormBuilder,
        private _Service: BorrowingService,
        private _ServiceUser: TimeAttendanceService,
        private _matDialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _authService: AuthService
    ) {
        const currentDate = new Date()
        this.filterForm = this._formBuilder.group({
            date: null,
            date_start: null,
            date_end: null,
            status: [null],
            user_id: null
        })
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {

        this.loadTable();
        this.GetUser()
    }
    GetUser() {
        this._ServiceUser.getUser('').subscribe((resp: any) => {
            this.itemtypeData = resp.data

        })
    }
    hiddenEdit() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 27);
        return menu.edit == 0;
    }
    hiddenDelete() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 27);
        return menu.delete == 0;
    }
    hiddenSave() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 27);
        return menu.save == 0;
    }
    pages = { current_page: 1, last_page: 1, per_page: 10, begin: 0 };
    loadTable(): void {
        const that = this;

        this.dtOptions = {
            pagingType: 'full_numbers',
            pageLength: 10,
            serverSide: true,
            processing: true,
            order: [[1, 'desc']],
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.11.3/i18n/th.json',
            },
            ajax: (dataTablesParameters: any, callback) => {
                // let formValue = { ...this.filterForm.value };

                // const formatDate = (date: any) => date ? moment(date).format('YYYY-MM-DD') : null;

                // ['date', 'date_start', 'date_end'].forEach(key => {
                //     formValue[key] = formatDate(formValue[key]);
                // });

                // const { status, user_id, date, date_start, date_end } = formValue;

                // Object.assign(dataTablesParameters, { status, user_id, date, date_start, date_end });

                that._Service
                    .getDataPage(dataTablesParameters)
                    .subscribe((resp) => {
                        this.dataRow = resp.data;
                        this.pages.current_page = resp.current_page;
                        this.pages.last_page = resp.last_page;
                        this.pages.per_page = resp.per_page;
                        if (resp.current_page > 1) {
                            this.pages.begin =
                                resp.per_page * resp.current_page - 1;
                        } else {
                            this.pages.begin = 0;
                        }
                        callback({
                            recordsTotal: resp.total,
                            recordsFiltered: resp.total,
                            data: [],
                        });
                        this._changeDetectorRef.markForCheck();
                    });
            },
            columns: [
                { data: 'action', orderable: false },
                { data: 'borrowing_code' },
                { data: 'full_name' },
                { data: 'borrow_date' },
                { data: 'expected_return_date' },
                { data: 'status' },
                { data: 'created_at' },
            ],
        };
    }

    /**
     * After view init
     */
    ngAfterViewInit(): void {
        // if (this._sort && this._paginator) {
        //     // Set the initial sort
        //     this._sort.sort({
        //         id: 'id',
        //         start: 'asc',
        //         disableClear: true
        //     });
        //     // Mark for check
        //     this._changeDetectorRef.markForCheck();
        //     // If the user changes the sort order...
        //     this._sort.sortChange
        //         .pipe(takeUntil(this._unsubscribeAll))
        //         .subscribe(() => {
        //             // Reset back to the first page
        //             this._paginator.pageIndex = 0;
        //             // Close the details
        //             this.closeDetails();
        //         });
        //     // Get products if sort or page changes
        //     merge(this._sort.sortChange, this._paginator.page).pipe(
        //         switchMap(() => {
        //             this.closeDetails();
        //             this.isLoading = true;
        //             return this._Service.getProducts(
        //                 this._paginator.pageIndex + 1,
        //                 this._paginator.pageSize,
        //                 this._sort.active,
        //                 this._sort.direction,
        //                 this.filterForm.value?.searchInputControl,
        //                 this.filterForm.value?.asset_type == 'default' ? '' : this.filterForm.value?.asset_type,
        //                 this.supplierId
        //             );
        //         }),
        //         map(() => {
        //             this.isLoading = false;
        //         })
        //     ).subscribe();
        // }
    }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    /**
     * Show flash message
     */
    showFlashMessage(type: 'success' | 'error'): void {
        // Show the message
        this.flashMessage = type;

        // Mark for check
        this._changeDetectorRef.markForCheck();

        // Hide it after 3 seconds
        setTimeout(() => {
            this.flashMessage = null;

            // Mark for check
            this._changeDetectorRef.markForCheck();
        }, 3000);
    }

    UpdateStatus(itemId: string, status: string) {
        const dialogRef = this._matDialog.open(EditComponent, {
            width: '500px',

            height: 'auto',
            data: {
                itemid: itemId,
                status: status,
            },
        });
        dialogRef.afterClosed().subscribe((item) => {
            this.rerender();
            this._changeDetectorRef.markForCheck();
        });
    }

    New() {
        const dialogRef = this._matDialog.open(DialogBorrowingFormComponent, {
            width: '90vw',
            maxWidth: '1200px',
            height: 'auto',
            maxHeight: 'max-height: 90vh',
            disableClose: true
        });

        dialogRef.afterClosed().subscribe((result) => {
            if (result) {
                this.rerender();
                this._changeDetectorRef.markForCheck();
            }
        });
    }

    Edit(borrowingData: any) {
        const dialogRef = this._matDialog.open(DialogBorrowingFormComponent, {
            width: '90vw',
            maxWidth: '1200px',
            height: 'auto',
            maxHeight: 'max-height: 90vh',
            disableClose: true,
            data: { borrowingData }
        });

        dialogRef.afterClosed().subscribe((result) => {
            if (result) {
                this.rerender();
                this._changeDetectorRef.markForCheck();
            }
        });
    }

    rerender(): void {
        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
            dtInstance.ajax.reload();
        });
    }

    Delete(id) {
        // const confirmation = this._fuseConfirmationService.open({
        //     title: 'ลบสิทธิ์การลา',
        //     message: 'คุณต้องการลบสิทธิ์การลาใช่หรือไม่ ',
        //     icon: {
        //         show: false,
        //         name: 'heroicons_outline:exclamation-triangle',
        //         color: 'warning',
        //     },
        //     actions: {
        //         confirm: {
        //             show: true,
        //             label: 'ยืนยัน',
        //             color: 'danger',
        //         },
        //         cancel: {
        //             show: true,
        //             label: 'ยกเลิก',
        //         },
        //     },
        //     dismissible: true,
        // });

        // // Subscribe to the confirmation dialog closed action
        // confirmation.afterClosed().subscribe((result) => {
        //     // If the confirm button pressed...
        //     if (result === 'confirmed') {
        //         this._Service
        //             .delete(id)
        //             .pipe(takeUntil(this.destroy$))
        //             .subscribe((res: any) => {
        //                 if (res.code == 201) {
        //                     this._fuseConfirmationService
        //                         .open({
        //                             title: 'ลบข้อมูลคลังสินค้า',
        //                             message: 'บันทึกเรียบร้อย',
        //                             icon: {
        //                                 show: true,
        //                                 name: 'heroicons_outline:check-circle',
        //                                 color: 'success',
        //                             },
        //                             actions: {
        //                                 confirm: {
        //                                     show: false,
        //                                     label: 'ตกลง',
        //                                     color: 'primary',
        //                                 },
        //                                 cancel: {
        //                                     show: false,
        //                                     label: 'ยกเลิก',
        //                                 },
        //                             },
        //                             dismissible: true,
        //                         })
        //                         .afterClosed()
        //                         .subscribe((res) => {
        //                             this.rerender();
        //                         });
        //                 }
        //             });
        //     }
        // });
    }

    onPersonnelSelected(selectedPersonnel: any): void {
        this.filterForm.patchValue({
            user_id: selectedPersonnel.id,
        })
    }
}
