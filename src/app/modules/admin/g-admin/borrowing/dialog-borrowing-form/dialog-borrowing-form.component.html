<div class="flex flex-col max-h-screen -m-6">
  <!-- Header -->
  <div class="flex flex-0 items-center justify-between h-16 pr-3 sm:pr-5 pl-6 sm:pl-8 bg-primary text-on-primary">
    <div class="text-lg font-medium">
      {{ isEditMode ? 'แก้ไขใบขอยืม' : 'สร้างใบขอยืมใหม่' }}
    </div>
    <button mat-icon-button (click)="onCancel()" [tabIndex]="-1">
      <mat-icon class="text-current" [svgIcon]="'heroicons_outline:x-mark'"></mat-icon>
    </button>
  </div>

  <!-- Content -->
  <form [formGroup]="borrowingForm" class="flex flex-col flex-auto p-6 sm:p-8">

    <!-- Basic Information -->
    <div class="flex flex-col gt-xs:flex-row">
      <mat-form-field class="flex-auto gt-xs:pr-3">
        <mat-label>ผู้ขอยืม</mat-label>
        <mat-select formControlName="user_id" required>
          <mat-option *ngFor="let user of users" [value]="user.id">
            {{ user.full_name || user.name }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="borrowingForm.get('user_id')?.hasError('required')">
          กรุณาเลือกผู้ขอยืม
        </mat-error>
      </mat-form-field>

      <mat-form-field class="flex-auto gt-xs:pl-3">
        <mat-label>สาขา</mat-label>
        <mat-select formControlName="branch_id" required>
          <mat-option *ngFor="let branch of branches" [value]="branch.id">
            {{ branch.name }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="borrowingForm.get('branch_id')?.hasError('required')">
          กรุณาเลือกสาขา
        </mat-error>
      </mat-form-field>
    </div>

    <!-- Dates -->
    <div class="flex flex-col gt-xs:flex-row">
      <mat-form-field class="flex-auto gt-xs:pr-3">
        <mat-label>วันที่ขอยืม</mat-label>
        <input matInput [matDatepicker]="borrowDatePicker" formControlName="borrow_date" required>
        <mat-datepicker-toggle matIconSuffix [for]="borrowDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #borrowDatePicker></mat-datepicker>
        <mat-error *ngIf="borrowingForm.get('borrow_date')?.hasError('required')">
          กรุณาเลือกวันที่ขอยืม
        </mat-error>
      </mat-form-field>

      <mat-form-field class="flex-auto gt-xs:pl-3">
        <mat-label>วันที่คาดว่าจะคืน</mat-label>
        <input matInput [matDatepicker]="returnDatePicker" formControlName="expected_return_date" required>
        <mat-datepicker-toggle matIconSuffix [for]="returnDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #returnDatePicker></mat-datepicker>
        <mat-error *ngIf="borrowingForm.get('expected_return_date')?.hasError('required')">
          กรุณาเลือกวันที่คาดว่าจะคืน
        </mat-error>
      </mat-form-field>
    </div>

    <!-- Purpose -->
    <mat-form-field>
      <mat-label>วัตถุประสงค์</mat-label>
      <textarea matInput formControlName="purpose" rows="3" required
                placeholder="เช่น สำหรับงานนำเสนอโครงการ"></textarea>
      <mat-error *ngIf="borrowingForm.get('purpose')?.hasError('required')">
        กรุณาระบุวัตถุประสงค์
      </mat-error>
    </mat-form-field>

    <!-- Notes -->
    <mat-form-field>
      <mat-label>หมายเหตุ</mat-label>
      <textarea matInput formControlName="notes" rows="2"
                placeholder="หมายเหตุเพิ่มเติม (ถ้ามี)"></textarea>
    </mat-form-field>

    <!-- Equipment Items Section -->
    <div class="mt-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium">รายการอุปกรณ์ที่ขอยืม</h3>
        <button mat-raised-button color="primary" type="button" (click)="addEquipmentItem()">
          <mat-icon>add</mat-icon>
          เพิ่มอุปกรณ์
        </button>
      </div>

      <!-- Equipment Items Table -->
      <div formArrayName="equipment_items" class="overflow-x-auto">
        <table mat-table [dataSource]="equipmentItems.controls" class="w-full">

          <!-- Equipment Selection Column -->
          <ng-container matColumnDef="select">
            <th mat-header-cell *matHeaderCellDef>อุปกรณ์</th>
            <td mat-cell *matCellDef="let item; let i = index">
              <div [formGroupName]="i">
                <mat-form-field class="w-full">
                  <mat-select formControlName="equipment_id" required>
                    <mat-option *ngFor="let equipment of equipments" [value]="equipment.id">
                      {{ equipment.name }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            </td>
          </ng-container>

          <!-- Quantity Column -->
          <ng-container matColumnDef="quantity">
            <th mat-header-cell *matHeaderCellDef>จำนวน</th>
            <td mat-cell *matCellDef="let item; let i = index">
              <div [formGroupName]="i">
                <mat-form-field class="w-full">
                  <input matInput type="number" formControlName="quantity" min="1" required>
                </mat-form-field>
              </div>
            </td>
          </ng-container>

          <!-- Condition Column -->
          <ng-container matColumnDef="condition_before">
            <th mat-header-cell *matHeaderCellDef>สภาพก่อนยืม</th>
            <td mat-cell *matCellDef="let item; let i = index">
              <div [formGroupName]="i">
                <mat-form-field class="w-full">
                  <mat-select formControlName="condition_before" required>
                    <mat-option value="excellent">ดีเยี่ยม</mat-option>
                    <mat-option value="good">ดี</mat-option>
                    <mat-option value="fair">พอใช้</mat-option>
                    <mat-option value="poor">ไม่ดี</mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            </td>
          </ng-container>

          <!-- Notes Column -->
          <ng-container matColumnDef="notes">
            <th mat-header-cell *matHeaderCellDef>หมายเหตุ</th>
            <td mat-cell *matCellDef="let item; let i = index">
              <div [formGroupName]="i">
                <mat-form-field class="w-full">
                  <input matInput formControlName="notes" placeholder="หมายเหตุ">
                </mat-form-field>
              </div>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>การดำเนินการ</th>
            <td mat-cell *matCellDef="let item; let i = index">
              <button mat-icon-button color="warn" type="button" (click)="removeEquipmentItem(i)">
                <mat-icon>delete</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <!-- No equipment items message -->
        <div *ngIf="equipmentItems.length === 0" class="flex flex-col items-center justify-center py-8 text-gray-500">
          <mat-icon class="text-6xl mb-2">inventory_2</mat-icon>
          <p>ยังไม่มีรายการอุปกรณ์</p>
          <p class="text-sm">คลิก "เพิ่มอุปกรณ์" เพื่อเพิ่มรายการ</p>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div class="flex items-center justify-end mt-8 space-x-4">
      <button mat-stroked-button type="button" (click)="onCancel()">
        ยกเลิก
      </button>
      <button mat-raised-button color="primary" type="submit"
              [disabled]="borrowingForm.invalid || isLoading"
              (click)="onSubmit()">
        <mat-icon *ngIf="isLoading" class="animate-spin">refresh</mat-icon>
        {{ isEditMode ? 'บันทึกการแก้ไข' : 'สร้างใบขอยืม' }}
      </button>
    </div>

  </form>
</div>
