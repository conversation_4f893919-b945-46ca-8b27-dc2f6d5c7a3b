<div class="sm:absolute sm:inset-0 flex flex-col flex-auto min-w-0 sm:overflow-auto bg-card dark:bg-transparent">
    <!-- Header -->
    <div
        class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b">
        <!-- Loader -->
        <div class="absolute inset-x-0 bottom-0" *ngIf="isLoading">
            <mat-progress-bar [mode]="'indeterminate'"></mat-progress-bar>
        </div>
        <!-- Title -->
        <div class="text-4xl font-extrabold tracking-tight">รายการเงินเดือน</div>
    </div>
    <!-- Main -->
    <div class="flex flex-col flex-auto min-w-0 bg-gray-100 dark:bg-transparent">

        <div class="flex flex-col p-3 sm:p-6">
            <div class="overflow-auto  bg-white shadow sm:rounded-lg">
                <!-- Products list -->
                <div class="flex mt-0 sm:mt-6 md:mt-6 justify-end px-4 gap-2 flex-wrap">

                    <button mat-flat-button (click)="newRoundSalary()"
                        class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4">
                        <mat-icon [svgIcon]="'heroicons_outline:plus'"></mat-icon>
                        <span class="ml-2 mr-1">สร้างรอบเงินเดือน</span>
                    </button>
                    <button mat-flat-button (click)="fetchAll()"
                        class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4">
                        <mat-icon [svgIcon]="'heroicons_outline:arrow-path'"></mat-icon>
                        <span class="ml-2 mr-1">ดึงข้อมูลใหม่</span>
                    </button>
                    <button mat-flat-button [disabled]="dataRow.length === 0" [ngClass]="{
    'bg-yellow-600 hover:bg-yellow-700 text-white': dataRow.length > 0,
    'bg-gray-400 text-gray-200 cursor-not-allowed': dataRow.length === 0
  }" class="rounded-lg py-2 px-4" (click)="runWithLoading()">
                        <mat-icon>autorenew</mat-icon>
                        <span class="ml-2 mr-1">ประมวลผล</span>
                    </button>
                    <!-- <button mat-flat-button
                        class="bg-green-700 text-gray-700 hover:bg-green-400 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
                        (click)="exportToBank()">
                        <mat-icon [svgIcon]="'heroicons_outline:document'"></mat-icon>
                        <span class="ml-2 mr-1">Excel สำหรับธนาคาร</span>
                    </button> -->
                    <button mat-flat-button
                        class="bg-green-700 text-gray-700 hover:bg-green-400 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
                        (click)="exportDailyReport()">
                        <mat-icon [svgIcon]="'heroicons_outline:document'"></mat-icon>
                        <span class="ml-2 mr-1">Excel</span>
                    </button>
                </div>
                <div class="flex flex-col flex-auto pt-5 overflow-auto sm:overflow-y-auto">
                    <form [formGroup]="formData">
                        <div class="border-b">
                            <div class="flex flex-col md:flex-row justify-between">
                                <div class="flex flex-col md:flex-row gap-2 w-full md:w-2/3 px-4">
                                    <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
                                        <mat-label
                                            class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/4 mt-4">เลือกปี</mat-label>
                                        <mat-select [formControlName]="'year'">
                                            <mat-option *ngFor="let year of years" [value]="year.value">
                                                {{ year.label }}
                                            </mat-option>
                                        </mat-select>
                                    </mat-form-field>
                                    <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
                                        <mat-label for="name"
                                            class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/4 mt-4">เลือกเดือน</mat-label>
                                        <mat-select [formControlName]="'month'">
                                            <mat-option value="01">มกราคม </mat-option>
                                            <mat-option value="02">กุมภาพันธ์</mat-option>
                                            <mat-option value="03">มีนาคม</mat-option>
                                            <mat-option value="04">เมษายน</mat-option>
                                            <mat-option value="05">พฤษภาคม</mat-option>
                                            <mat-option value="06">มิถุนายน</mat-option>
                                            <mat-option value="07">กรกฏาคม</mat-option>
                                            <mat-option value="08">สิงหาคม</mat-option>
                                            <mat-option value="09">กันยายน</mat-option>
                                            <mat-option value="10">ตุลาคม</mat-option>
                                            <mat-option value="11">พฤศจิกายน</mat-option>
                                            <mat-option value="12">ธันวาคม</mat-option>
                                        </mat-select>
                                    </mat-form-field>
                                    <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
                                        <mat-label for="name"
                                            class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/4 mt-4">เลือกรอบ</mat-label>
                                        <mat-select formControlName="round">
                                            <mat-option value="1">ครึ่งเดือนแรก </mat-option>
                                            <mat-option value="2">ครึ่งเดือนหลัง</mat-option>
                                        </mat-select>
                                    </mat-form-field>
                                    <!-- <mat-form-field class="w-full">
                                        <mat-label for="name"
                                            class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/4 mt-4">ชื่อพนักงาน</mat-label>
                                        <mat-select [formControlName]="'user_id'">
                                            <mat-option *ngFor="let item of UserList" [value]="item.id">
                                                {{item.first_name}} {{item.last_name}}
                                            </mat-option>
                                        </mat-select>
                                    </mat-form-field> -->
                                    <div class="w-full pt-1">
                                        <mat-label for="name"
                                            class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/4 mt-4">เลือกพนักงาน</mat-label>
                                        <ng-container *ngIf="UserList">
                                            <app-personnel-autocomplete [itemtypeData]="UserList"
                                                (personnelSelected)="onPersonnelSelected($event)">
                                            </app-personnel-autocomplete>
                                        </ng-container>
                                    </div>
                                </div>
                                <div class="flex mt-0 sm:mt-6 md:mt-6 justify-end px-4 gap-2">
                                    <button mat-flat-button
                                        class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
                                        (click)="Search()">
                                        <mat-icon [svgIcon]="'heroicons_outline:magnifying-glass'"></mat-icon>
                                        <span class="ml-2 mr-1">ค้นหา</span>
                                    </button>
                                    <button mat-flat-button
                                        class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
                                        (click)="Clear()">
                                        <mat-icon [svgIcon]="'heroicons_outline:arrow-path'"></mat-icon>
                                        <span class="ml-2 mr-1">ล้าง</span>
                                    </button>
                                </div>
                            </div>


                        </div>
                    </form>
                </div>
                <div class="flex flex-col flex-auto p-5 sm:overflow-auto overflow-x-scroll">
                    <div class="w-full overflow-x-auto">
                        <table datatable [dtOptions]="dtOptions"
                            class="min-w-[1000px] w-full text-sm text-left text-gray-700 border border-gray-200 bg-white">
                            <thead class="bg-gray-300 text-black">
                                <tr>
                                    <th class="text-center">พิมพ์สลิป</th>
                                    <th class="text-center">ลำดับ</th>
                                    <th class="text-center">ชื่อ-สกุล</th>
                                    <th class="text-right">เงินเพิ่ม</th>
                                    <th class="text-right">เงินหัก</th>
                                    <th class="text-right">ประกันสังคม</th>
                                    <th class="text-right">กองทุน</th>
                                    <th class="text-right">เงินเดือน</th>
                                    <th class="text-center">OT</th>
                                    <th class="text-center">รายได้สุทธิ</th>
                                    <th class="text-center">มาสายทั้งหมด</th>
                                </tr>
                            </thead>
                            <tbody *ngIf="dataRow?.length > 0; else noData">
                                <tr *ngFor="let item of dataRow; let i = index" class="hover:bg-gray-100">
                                    <td class="text-center whitespace-nowrap">
                                        <ng-container
                                            *ngIf="formData.value.year && formData.value.month && formData.value.round; else disableActions">
                                            <button mat-icon-button (click)="print(item)" [disabled]="hiddenSave()"
                                                matTooltip="พิมพ์สลิป">
                                                <mat-icon>print</mat-icon>
                                            </button>
                                            <button mat-icon-button (click)="viewDeatail(item)"
                                                [disabled]="hiddenSave()" matTooltip="ดูข้อมูล">
                                                <mat-icon>info</mat-icon>
                                            </button>
                                        </ng-container>
                                        <ng-template #disableActions>
                                            <button mat-icon-button disabled
                                                matTooltip="พิมพ์สลิป"><mat-icon>print</mat-icon></button>
                                            <button mat-icon-button disabled
                                                matTooltip="ดูข้อมูล"><mat-icon>info</mat-icon></button>
                                        </ng-template>
                                    </td>
                                    <td class="text-center">{{ pages.begin + (i + 1) }}</td>
                                    <td class="min-w-[200px]">{{ item?.user.fullname }}</td>
                                    <td class="text-green-500 text-right">{{ item.total_income | number: '1.2-2' }}</td>
                                    <td class="text-red-500 text-right">{{ item.total_deduct | number: '1.2-2' }}</td>
                                    <td class="text-red-500 text-right">{{ item.sso_total ?? 0 | number: '1.2-2' }}</td>
                                    <td class="text-red-500 text-right">{{ item.pvd_total ?? 0 | number: '1.2-2' }}</td>
                                    <td class="text-green-500 text-right">{{ item.salary | number: '1.2-2' }}</td>
                                    <td class="text-blue-700 text-center">{{ item.ot | number: '1.2-2' }}</td>
                                    <td class="text-blue-700 text-right">{{ item.total | number: '1.2-2' }}</td>
                                    <td class="text-red-700 text-center">{{ item.total_late ?? 0 | number: '1.' }}</td>
                                </tr>
                            </tbody>
                            <ng-template #noData>
                                <tr>
                                    <td colspan="10" class="text-center py-4 text-gray-500">ไม่มีข้อมูล !</td>
                                </tr>
                            </ng-template>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>