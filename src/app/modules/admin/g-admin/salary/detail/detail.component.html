<div class="flex flex-col gap-3">
    <div class="flex justify-between items-center ">
        <span class="font-bold text-2xl">รายละเอียดเงินได้/เงินหัก</span>
        <div class="flex gap-5">
            <button mat-flat-button (click)="editBond()"
                class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4">
                <mat-icon [svgIcon]="'heroicons_outline:pencil'"></mat-icon>
                <span class="ml-2 mr-1">แก้ไขเงินประกัน</span>
            </button>
            <button mat-flat-button (click)="edit()"
                class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4">
                <mat-icon [svgIcon]="'heroicons_outline:pencil'"></mat-icon>
                <span class="ml-2 mr-1">แก้ไข</span>
            </button>
            <button mat-flat-button (click)="fetchID()"
                class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4">
                <mat-icon [svgIcon]="'heroicons_outline:arrow-path'"></mat-icon>
                <span class="ml-2 mr-1">ดึงข้อมูลใหม่</span>
            </button>

        </div>
    </div>
    <div class="flex flex-col w-full rounded-md gap-1">
        <span class="text-xl font-bold">ข้อมูลพนักงาน</span>
        <div class="flex">
            <!-- ข้อมูลส่วนตัว -->
            <div class="grid grid-cols-[130px_1fr] w-1/2 gap-y-1">
                <span class="font-semibold text-gray-700">ชื่อพนักงาน</span>
                <span class="text-gray-900">{{ data?.user?.fullname }}</span>

                <span class="font-semibold text-gray-700">แผนก</span>
                <span class="text-gray-900">{{ data?.user?.department.name }}</span>

                <span class="font-semibold text-gray-700">ตำแหน่ง</span>
                <span class="text-gray-900">{{ data?.user?.position.name }}</span>

                <span class="font-semibold text-gray-700">สาขา</span>
                <span class="text-gray-900">{{ data?.user?.branch.name }}</span>

                <span class="font-semibold text-gray-700">สถานะเงินประกัน</span>
                <span
                    [ngClass]="data?.user?.is_bond_complete == 1 ? 'text-green-600 font-medium' : 'text-red-600 font-medium'">
                    {{ data?.user?.is_bond_complete == 1 ? 'จ่ายครบแล้ว' : 'ยังไม่ครบ' }}
                </span>
            </div>

            <!-- ข้อมูลการเงิน -->
            <div class="grid grid-cols-[130px_1fr] w-1/2 gap-y-1">
                <span class="font-semibold text-gray-700">เงินเดือน</span>
                <span class="text-gray-900">{{ data?.user?.salary | number:'1.2-2' }}</span>

                <span class="font-semibold text-gray-700">เงินได้รวม</span>
                <span class="text-gray-900">{{ data?.total_income ?? 0 | number:'1.2-2' }}</span>

                <span class="font-semibold text-gray-700">เงินหักรวม</span>
                <span class="text-gray-900">{{ data?.total_deduct ?? 0 | number:'1.2-2' }}</span>

                <span class="font-semibold text-gray-700">รวมทั้งหมด</span>
                <span class="text-gray-900">{{ data?.total ?? 0 | number:'1.2-2' }}</span>

                <span class="font-semibold text-gray-700">เงินประกันคงเหลือ</span>
                <span class="text-gray-900">
                    {{ data?.user?.bond_balance != null ? (data?.user?.bond_balance | number:'1.2-2') : '-' }}
                </span>
            </div>
        </div>

    </div>
    <div class="flex gap-5">
        <div class="flex flex-col w-1/2 overflow-auto rounded-md">
            <span class="text-xl font-bold">เงินได้</span>
            <table class="w-full ">
                <thead>
                    <tr class="bg-gray-300 border-gray-300 border">
                        <th class="p-3 whitespace-nowrap">รายการ</th>
                        <th class="p-3 whitespace-nowrap">จำนวนเงิน</th>
                        <th class="p-3 whitespace-nowrap">หมายเหตุ</th>
                    </tr>
                </thead>
                <tbody>
                    <ng-container *ngFor="let item of data?.income_paids;">
                        <tr>
                            <td class="py-2 px-3 whitespace-nowrap text-center">{{item?.income_type?.name}}</td>
                            <td class="py-2 px-3 whitespace-nowrap text-center text-green-400">{{item?.price |
                                number:'1.2'}}</td>
                            <td class="py-2 px-3 whitespace-nowrap text-center"> {{item?.income_type?.description}}</td>
                        </tr>
                    </ng-container>
                    <tr *ngIf="data?.income_paids?.length == 0">
                        <td colspan="3" class="text-center p-2">ไม่มีข้อมูล</td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="flex flex-col w-1/2 overflow-auto rounded-md ">
            <span class="text-xl font-bold">เงินหัก</span>
            <table class="w-full ">
                <thead>
                    <tr class="bg-gray-300 border-gray-300 border">
                        <th class="p-3 whitespace-nowrap">รายหาร</th>
                        <th class="p-3 whitespace-nowrap">จำนวนเงิน</th>
                        <th class="p-3 whitespace-nowrap">หมายเหตุ</th>
                    </tr>
                </thead>
                <tbody>
                    <ng-container *ngFor="let item of data?.deduct_paids;">
                        <tr>
                            <td class="py-2 px-3 whitespace-nowrap text-center">{{item.deduct_type.name}}</td>
                            <td class="py-2 px-3 whitespace-nowrap text-center text-red-400">{{item.price |
                                number:'1.2'}}</td>
                            <td class="py-2 px-3 whitespace-nowrap text-center"> {{item.deduct_type.description}}</td>
                        </tr>
                    </ng-container>
                    <tr *ngIf="data?.deduct_paids?.length == 0">
                        <td colspan="3" class="text-center p-2">ไม่มีข้อมูล</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>