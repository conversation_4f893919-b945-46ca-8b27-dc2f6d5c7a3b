import { ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { SalaryService } from '../salary.service';
import { CommonModule, DecimalPipe, NgFor, NgIf } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButton, MatIconButton } from '@angular/material/button';
import { MatOption } from '@angular/material/core';
import { MatFormField, MatLabel } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatProgressBar } from '@angular/material/progress-bar';
import { MatSelect } from '@angular/material/select';
import { DataTablesModule } from 'angular-datatables';
import { EditIncomeComponent } from '../edit-income/edit-income.component';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { EditBondComponent } from '../edit-bond/edit-bond.component';

@Component({
  selector: 'app-detail',
  imports: [CommonModule, NgIf, FormsModule, MatButton, ReactiveFormsModule, NgFor, MatButton, MatIcon, DataTablesModule],
  templateUrl: './detail.component.html',
  styleUrl: './detail.component.scss'
})
export class DetailComponent implements OnInit {
    data: any;
    constructor(
        @Inject(MAT_DIALOG_DATA) private _data,
        private _service: SalaryService,
        private _matDialog: MatDialog,
        private _fuseConfirmationService: FuseConfirmationService,

    ) {}
    ngOnInit(): void {
        this._service.getPaidSalaryByID(this._data.id).subscribe(data => {
            this.data = data
        })
    }
    edit() {
        const dialogRef = this._matDialog.open(EditIncomeComponent, {
            width: '800px',
            height: 'auto',
            data: {
                id: this.data.id,
                data:{
                "salary": this.data.salary,
                "total_ot": this.data.ot,
                "total_withdraw_salary": 0,
                "income_paids": this.data.income_paids,
                "deduct_paids": this.data.deduct_paids
            },
            }
        });
        dialogRef.afterClosed().subscribe((item) => {
            this._service.getPaidSalaryByID(this._data.id).subscribe(data => {
            this.data = data
        })
        });
    }
    editBond() {
        const dialogRef = this._matDialog.open(EditBondComponent, {
            width: '800px',
            height: 'auto',
            data: {
                id: this.data.user.id,
                data:{
                "status": this.data.user.is_bond_complete,
                "balance": this.data.user.bond_balance,
            },
            }
        });
        dialogRef.afterClosed().subscribe((item) => {
            this._service.getPaidSalaryByID(this._data.id).subscribe(data => {
            this.data = data
        })
        });
    }

    fetchID() {
        const confirmation = this._fuseConfirmationService.open({
            title: 'ดึงข้อมูล',
            message: 'คุณต้องการดึงข้อมูลใช่หรือไม่ ',
            icon: {
                show: false,
                name: 'heroicons_outline:exclamation-triangle',
                color: 'warning',
            },
            actions: {
                confirm: {
                    show: true,
                    label: 'ยืนยัน',
                    color: 'danger',
                },
                cancel: {
                    show: true,
                    label: 'ยกเลิก',
                },
            },
            dismissible: true,
        });

        // Subscribe to the confirmation dialog closed action
        confirmation.afterClosed().subscribe((result) => {

            if (result === 'confirmed') {

                this._service.getFetchId(this.data.id, { "income_paids": this.data.income_paids, "deduct_paids": this.data.deduct_paids }).subscribe({
                    next: (res: any) => {
                        this._service.getPaidSalaryByID(this._data.id).subscribe(data => {
                            this.data = data
                        })
                    },
                    error: (err) => {
                        console.error('Error:', err);
                        this._fuseConfirmationService.open({
                            title: 'เกิดข้อผิดพลาด',
                            message: 'ไม่สามารถดึงข้อมูลใหม่ได้',
                            icon: {
                                show: true,
                                name: 'heroicons_outline:exclamation-circle',
                                color: 'warn',
                            },
                            actions: {
                                confirm: {
                                    show: true,
                                    label: 'ตกลง',
                                    color: 'warn',
                                }
                            },
                            dismissible: true,
                        })
                    }
                });
            }
        })
    }
}
