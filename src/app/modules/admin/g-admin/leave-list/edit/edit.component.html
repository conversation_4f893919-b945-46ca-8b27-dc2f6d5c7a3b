<div class="flex flex-col flex-auto min-w-0 bg-card dark:bg-transparent">
    <!-- Header -->
    <div
        class="flex flex-col sm:flex-row items-start sm:items-center sm:justify-between px-2 py-2 sm:px-2 sm:py-2 border-b bg-card">
        <h2 class="text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10">รายการลา</h2>
    </div>

    <!-- Main -->
    <div class="flex-auto p-2 sm:p-2">
        <form [formGroup]="formData" class="max-w-3xl mx-auto">
            <div
                class="rounded-xl bg-white/70 dark:bg-gray-800/60 shadow-sm ring-1 ring-gray-100 dark:ring-gray-700 px-4 py-8 sm:px-4 sm:py-10 space-y-8">

                <!-- สถานะ -->
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 items-start">
                    <label class="text-gray-900 dark:text-gray-100 font-semibold pt-2.5">สถานะ</label>
                    <div class="sm:col-span-2">
                        <mat-form-field appearance="outline" class="w-full">
                            <mat-label>เลือกสถานะ</mat-label>
                            <mat-select formControlName="status">
                                <mat-option value="approved">อนุมัติ</mat-option>
                                <mat-option value="head_cancel">ไม่อนุมัติ</mat-option>
                                <mat-option value="cancel">ยกเลิก</mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                </div>

                <!-- รายละเอียด -->
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 items-start">
                    <label class="text-gray-900 dark:text-gray-100 font-semibold pt-2.5">รายละเอียด</label>
                    <div class="sm:col-span-2">
                        <mat-form-field appearance="outline" class="w-full">
                            <mat-label>รายละเอียด</mat-label>
                            <textarea matInput rows="5" placeholder="รายละเอียดเพิ่มเติม..."
                                formControlName="remark"></textarea>
                        </mat-form-field>
                    </div>
                </div>

                <!-- Actions -->
                <div
                    class="flex items-center justify-center pt-8 mt-6 border-t border-gray-100 dark:border-gray-700 gap-4">
                    <button type="button" mat-stroked-button color="accent" (click)="onClose()"
                        class="min-w-[120px] px-4 py-2">
                        <mat-icon svgIcon="heroicons_solid:x-mark" class="mr-2"></mat-icon>
                        ยกเลิก
                    </button>

                    <button type="button" mat-stroked-button color="primary" (click)="Approve()"
                        class="min-w-[120px] px-4 py-2">
                        <mat-icon svgIcon="heroicons_solid:check" class="mr-2"></mat-icon>
                        ยืนยัน
                    </button>

                </div>

            </div>
        </form>
    </div>
</div>