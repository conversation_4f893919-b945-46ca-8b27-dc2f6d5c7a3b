import { Route, Routes } from '@angular/router';




export default [

    {
        path: '',
        loadComponent: () => import('./leave-list.component').then(m => m.LeaveListComponent),
        children: [
            {
                path: 'list',
                loadComponent: () => import('./list/list.component').then(m => m.ListComponent),
        
            },
            {
                path: 'form',
                loadComponent: () => import('./leave/form.component').then(m => m.FormLeaveComponent),
        
            },
         


        ]
    }
] as Routes
