<div class="sm:absolute sm:inset-0 flex flex-col flex-auto min-w-0 sm:overflow-auto bg-card dark:bg-transparent">

    <!-- Header -->
    <div
        class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b">
        <!-- Loader -->
        <div class="absolute inset-x-0 bottom-0" *ngIf="isLoading">
            <mat-progress-bar [mode]="'indeterminate'"></mat-progress-bar>
        </div>
        <!-- Title -->
        <div class="text-4xl font-extrabold tracking-tight">รายการลา</div>
        <!-- Actions -->
    </div>
    <!-- Main -->
    <div class="flex flex-col flex-auto min-w-0 bg-gray-100 dark:bg-transparent">

        <div class="flex flex-col p-3 sm:p-6">
            <div class="overflow-auto  bg-white shadow sm:rounded-lg">
                <div class="flex flex-col md:flex-row justify-between py-2 px-5">
                    <form [formGroup]="filterForm" class="flex flex-col md:flex-row w-full">
                        <div class="flex flex-col md:flex-row justify-start items-center w-full mt-5 gap-2">
                            <div
                                class="flex text-md font-normal tracking-tight leading-7 sm:leading-10 truncate justify-center items-center w-full">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>สถานะ</mat-label>
                                    <mat-select [formControlName]="'status'" placeholder="เลือกสถานะ"
                                        (selectionChange)="this.rerender()">
                                        <mat-option *ngFor="let item of status" [value]="item.value">
                                            {{item.name}}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </div>
                            <div
                                class="flex text-md font-normal tracking-tight leading-7 sm:leading-10 truncate justify-center items-center w-full">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>วันที่ขอ</mat-label>
                                    <input matInput [matDatepicker]="pickDate" [formControlName]="'date'"
                                        (dateChange)="this.rerender()">
                                    <mat-datepicker-toggle matSuffix [for]="pickDate"></mat-datepicker-toggle>
                                    <mat-datepicker #pickDate></mat-datepicker>
                                </mat-form-field>
                            </div>
                            <div
                                class="flex text-md font-normal tracking-tight leading-7 sm:leading-10 truncate justify-center items-center w-full">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>วันที่ลา</mat-label>
                                    <mat-date-range-input [rangePicker]="picker">
                                        <input matStartDate formControlName="date_start" placeholder="วันที่เริ่มต้น">
                                        <input matEndDate formControlName="date_end" placeholder="วันที่สิ้นสุด"
                                            (dateChange)="this.rerender()">
                                    </mat-date-range-input>
                                    <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
                                    <mat-date-range-picker #picker></mat-date-range-picker>
                                </mat-form-field>
                            </div>
                            <div class="flex flex-col w-full">
                                <ng-container *ngIf="itemtypeData">
                                    <label>เลือกพนักงาน</label>
                                    <app-personnel-autocomplete [itemtypeData]="itemtypeData"
                                        (personnelSelected)="onPersonnelSelected($event)"
                                        (selectionChange)="rerender()">
                                    </app-personnel-autocomplete>
                                </ng-container>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="flex flex-col flex-auto p-5 sm:overflow-auto overflow-x-scroll">

                    <table datatable [dtOptions]="dtOptions"
                        class="table w-full text-left text-gray-500 overflow-hidden whitespace-nowrap">
                        <thead class="bg-gray-300 text-black">
                            <tr>
                                <th class="text-center">จัดการ</th>
                                <th>ลำดับ</th>
                                <th>ชื่อ - นามสกุล</th>
                                <th>ประเภทการลา</th>
                                <th>วันที่แจ้ง</th>
                                <th>ตั้งแต่วันที่</th>
                                <th>ถึงวันที่</th>
                                <th>ตั้งแต่เวลา</th>
                                <th>ถึงเวลา</th>
                                <th>รายละเอียด</th>
                                <th>จำนวนวัน</th>
                                <th class="text-center">สถานะ</th>
                            </tr>
                        </thead>
                        <tbody *ngIf="dataRow?.length != 0">
                            <tr *ngFor="let item of dataRow; let i = index"
                                class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 text-md py-15">
                                <td class="text-center">
                                    <!-- ปุ่มเปิดเมนู -->
                                    <button mat-icon-button [matMenuTriggerFor]="actionMenu" [disabled]="hiddenEdit()"
                                        matTooltip="ตัวเลือก">
                                        <mat-icon>fact_check</mat-icon>
                                    </button>

                                    <!-- เมนูตัวเลือก -->
                                    <mat-menu #actionMenu="matMenu">
                                        <button mat-menu-item (click)="UpdateStatus(item.id, 'approved')"
                                            *ngIf="item.status === 'process' || item.status === 'open'">
                                            <mat-icon class="text-green-500">check_circle</mat-icon>
                                            <span>อนุมัติ</span>
                                        </button>
                                        <button mat-menu-item (click)="UpdateStatus(item.id, 'head_cencal')"
                                            *ngIf="item.status === 'process' || item.status === 'open'">
                                            <mat-icon class="text-red-500">cancel</mat-icon>
                                            <span>ไม่อนุมัติ</span>
                                        </button>
                                        <button mat-menu-item (click)="UpdateStatus(item.id, 'cancel')">
                                            <mat-icon class="text-slate-500">cancel</mat-icon>
                                            <span>ยกเลิก</span>
                                        </button>
                                    </mat-menu>
                                </td>

                                <td style="width: 5%;">{{ pages.begin + (i + 1) }}</td>
                                <td> {{item.user ? item.user?.first_name: '-'}} {{item.user ? item.user?.last_name:
                                    '-'}}</td>
                                <td> {{item.leave_type ? item.leave_type?.name: '-'}} </td>
                                <td>
                                    {{
                                    item.created_at !== null
                                    ? (item.created_at | buddhistDate : "dd/MM/yyyy")
                                    : "-"
                                    }}
                                </td>
                                <td>
                                    {{
                                    item.date_start !== null
                                    ? (item.date_start | buddhistDate : "dd/MM/yyyy")
                                    : "-"
                                    }}
                                </td>
                                <td>
                                    {{
                                    item.date_end !== null
                                    ? (item.date_end | buddhistDate : "dd/MM/yyyy")
                                    : "-"
                                    }}
                                </td>
                                <td> {{item.time_start ? item.time_start: '-'}} </td>
                                <td> {{item.time_end ? item.time_end: '-'}} </td>
                                <td> {{item.description ? item.description: '-'}} </td>
                                <td>{{item.qty_day ? item.qty_day : '-'}}</td>
                                <td class="text-center">
                                    <div class=" text-blue-500 text-md font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-green-200 dark:text-green-900"
                                        *ngIf="item.status === 'open'">
                                        รอหัวหน้าอนุมัติ
                                    </div>
                                    <div class=" text-green-500 text-md font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-green-200 dark:text-green-900"
                                        *ngIf="item.status === 'process'">
                                        หัวหน้าอนุมัติ
                                    </div>
                                    <div class=" text-green-500 text-md font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-green-200 dark:text-green-900"
                                        *ngIf="item.status === 'approved'">
                                        อนุมัติ
                                    </div>
                                    <div class=" text-red-500 text-md font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-green-200 dark:text-green-900"
                                        *ngIf="item.status === 'cancel'">
                                        ยกเลิก
                                    </div>
                                    <div class=" text-red-500 text-md font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-green-200 dark:text-green-900"
                                        *ngIf="item.status === 'head_cancel'">
                                        หัวหน้าไม่อนุมัติ
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                        <tbody *ngIf="dataRow?.length == 0">
                            <tr>
                                <td colspan="12" class="no-data-available text-center">ไม่มีข้อมูล !</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>