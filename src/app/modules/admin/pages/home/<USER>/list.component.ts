import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    OnDestroy,
    OnInit,
    ViewChild,
    ViewEncapsulation,
    ElementRef,
    NgZone,
} from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import {
    debounceTime,
    map,
    merge,
    Observable,
    Subject,
    switchMap,
    takeUntil,
} from 'rxjs';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from 'environments/environment';
import { AuthService } from 'app/core/auth/auth.service';
import { sortBy, startCase } from 'lodash-es';
import {
    AssetType,
    BranchPagination,
    DataBranch,
    Topsale,
    deadstock,
} from '../home.types';
import { HomeService } from '../home.service';
// import { ImportOSMComponent } from '../card/import-osm/import-osm.component';
import { MatTableDataSource } from '@angular/material/table';
import { DataTableDirective } from 'angular-datatables';

import Chart from 'chart.js/auto';
import { ApexAxisChartSeries, ApexChart, ApexXAxis, ApexStroke, ApexTooltip, ApexDataLabels, ApexTitleSubtitle, NgApexchartsModule } from 'ng-apexcharts';
import { MatFormField, MatLabel } from '@angular/material/form-field';
import { NgClass, NgFor, DecimalPipe, CurrencyPipe, CommonModule } from '@angular/common';
import { MatSelect } from '@angular/material/select';
import { MatOption } from '@angular/material/autocomplete';

export type ChartOptionsSpline = {
    series: ApexAxisChartSeries;
    chart: ApexChart;
    xaxis: ApexXAxis;
    stroke: ApexStroke;
    tooltip: ApexTooltip;
    dataLabels: ApexDataLabels;
    title: ApexTitleSubtitle;
    subtitle: ApexTitleSubtitle;
};
@Component({
    selector: 'home-list',
    templateUrl: './list.component.html',
    styleUrls: ['./list.component.scss'],
    // encapsulation: ViewEncapsulation.None,
    // changeDetection: ChangeDetectionStrategy.OnPush,
    animations: fuseAnimations,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [FormsModule, ReactiveFormsModule, MatFormField, NgClass, MatLabel, MatSelect, NgFor, MatOption, NgApexchartsModule, DecimalPipe, CurrencyPipe, CommonModule]
})


export class ListComponent implements OnInit, AfterViewInit, OnDestroy {
    formFieldHelpers: string[] = ['fuse-mat-dense'];
    @ViewChild('doughnutCanvas') doughnutCanvas: ElementRef | undefined;
    doughnutChart: any;
    @ViewChild('lineCanvas') lineCanvas: ElementRef | undefined;
    lineChart: any;
    @ViewChild('barCanvas') barCanvas: ElementRef | undefined;
    barChart: any;
    public chartOptions4: Partial<ChartOptionsSpline>;

    @ViewChild(DataTableDirective)
    dtElement!: DataTableDirective;
    public dtOptions: DataTables.Settings = {};
    public dataRow: any[];
    filterForm: FormGroup;
    // dataRow: any = []
    @ViewChild(MatPaginator) _paginator: MatPaginator;
    @ViewChild(MatSort) private _sort: MatSort;
    displayedColumns: string[] = [
        'id',
        'name',
        'status',
        'create_by',
        'created_at',
        'actions',
    ];
    dataSource: MatTableDataSource<DataBranch>;

    products$: Observable<any>;
    asset_types: AssetType[];
    flashMessage: 'success' | 'error' | null = null;
    isLoading: boolean = false;
    searchInputControl: FormControl = new FormControl();
    selectedProduct: any | null = null;

    tagsEditMode: boolean = false;
    private _unsubscribeAll: Subject<any> = new Subject<any>();
    env_path = environment.API_URL;

    topsale$: Observable<Topsale[]>;
    deadstock$: Observable<deadstock[]>;
    data1: any;

    boo1: any;
    deadstock: any;
    month: any[] = [];
    total: any[] = [];
    monthreturn: any[] = [];
    totalreturn: any[] = [];
    me: any | null;
    get roleType(): string {
        return 'marketing';
    }
    currentDate = new Date();
    totalEmployees = 50;
    presentToday = 45;
    pendingLeaves = 4;
    lateToday = 3;

    recentLeaves = [
        { employee: 'John Doe', type: 'Sick Leave', date: new Date('2025-07-30'), status: 'Pending' },
        { employee: 'Jane Smith', type: 'Annual Leave', date: new Date('2025-07-29'), status: 'Approved' },
        { employee: 'Robert Brown', type: 'Work from Home', date: new Date('2025-07-28'), status: 'Rejected' },
    ];
    supplierId: string | null;
    pagination: BranchPagination;
    currentYear: number = new Date().getFullYear();
    years: number[] = [];
    dashboard: any;

    employeeGrowth = 5.6; // % growth from last month

    newEmployees = 12;
    newHireChange = 2.4; // % from last month

    resignedEmployees = 4;
    resignationChange = -1.2; // % from last month

    leaveRequests = 9;
    leaveRequestChange = 3.7; // % pending approval

    // Attendance Issues
    lateEmployees: {
        name: string;
        avatar?: string;
        department: string;
        status: 'Late' | 'Absent' | 'Early Leave';
    }[] = [
            {
                name: 'Siriwat K.',
                department: 'Sales',
                status: 'Late',
                avatar: 'https://randomuser.me/api/portraits/men/32.jpg'
            },
            {
                name: 'Nalinee P.',
                department: 'HR',
                status: 'Absent',
                avatar: ''
            },
            {
                name: 'Thanakorn J.',
                department: 'Engineering',
                status: 'Early Leave',
                avatar: 'https://randomuser.me/api/portraits/men/44.jpg'
            }
        ];

    // Announcements
    announcements: {
        title: string;
        summary: string;
        date: string;
        author: string;
    }[] = [
            {
                title: 'Company Town Hall Meeting',
                summary: 'Join us for the quarterly town hall to discuss our progress and upcoming goals.',
                date: 'July 25, 2025',
                author: 'HR Department'
            },
            {
                title: 'New Leave Policy Effective Aug 2025',
                summary: 'We have updated our leave policy to better accommodate team flexibility.',
                date: 'July 20, 2025',
                author: 'Admin Office'
            }
        ];

    /**
     * Constructor
     */
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _ngZone: NgZone,
        private _fuseConfirmationService: FuseConfirmationService,
        private _formBuilder: FormBuilder,
        // private _Service: PermissionService,
        private _Service: HomeService,
        private _matDialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _authService: AuthService
    ) {
        // this.dashboard = this._activatedRoute.snapshot.data.dashboard.data

        this.generateYears();
        this.filterForm = this._formBuilder.group({
            year: [this.currentYear], // กำหนดค่าเริ่มต้นเป็นปีปัจจุบัน
            channal: null,
            page_id: null
        });

        // const data = this.dashboard.map(item => item.total)



    }

    generateYears() {
        this.years = Array.from({ length: 5 }, (_, i) => this.currentYear - i);
        // this.filterForm.patchValue({
        //     year: this.years[0]
        // })
    }

    selectedYear() {
        this._Service.getDashboard(this.filterForm.value).subscribe((resp: any) => {
            const data = resp.data.map(item => item.total)
            this.chartOptions4 = {
                title: {
                    text: "ยอดขายแต่ละเดือน",
                    align: "left",
                    style: {
                        fontSize: '19px',
                        fontWeight: '700',
                        color: 'black'
                    }
                },
                subtitle: {
                    text: '',//`วันที่ ${this.datepipe.transform(new Date(), 'dd/MM/yyyy')}`,
                    align: "left"
                },
                series: [
                    {
                        name: "series1",
                        data: data, // Monthly sales data for series1
                    }
                ],
                chart: {
                    height: 400,
                    type: "area",
                    toolbar: {
                        tools: {
                            zoom: false,
                            zoomin: false,
                            zoomout: false,
                            pan: false,
                            reset: false,
                        }
                    }
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    curve: "smooth"
                },
                xaxis: {
                    type: "category",
                    categories: [
                        "มกราคม", "กุมภาพันธ์", "มีนาคม", "เมษายน", "พฤษภาคม", "มิถุนายน",
                        "กรกฎาคม", "สิงหาคม", "กันยายน", "ตุลาคม", "พฤศจิกายน", "ธันวาคม"
                    ], // Monthly categories
                    labels: {
                        style: {
                            colors: [
                                "#955a9c", "#33FF57", "#3357FF", "#efb8fc", "#00bff1", "#FEB019",
                                "#FF4560", "#775DD0", "#1ff7b8", "#AF6E5A", "#26a69a", "#FF5733"
                            ],
                            fontSize: "12px"
                        }
                    }
                }
            };
            this._changeDetectorRef.markForCheck()
        })
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {
        // this._Service.getReportItemTopSale().subscribe((res: any) => {
        //     this.boo1 = res.data;
        // });
        this.loadTable();
        // this._Service.getBranch().subscribe((resp: any) => {
        //     this.dataRow = resp.data;
        //     this.dataSource = new MatTableDataSource(this.dataRow)
        //     this.dataSource.paginator = this._paginator;
        //     this.dataSource.sort = this._sort;
        //     this._changeDetectorRef.markForCheck();
        // })
        this.topsale$ = this._Service.topsale$;

        // this._Service.getReportItemDeadStock().subscribe((res: any) => {
        //     this.deadstock = res.data;
        // });
        this.loadTable();
        // this._Service.getBranch().subscribe((resp: any) => {
        //     this.dataRow = resp.data;
        //     this.dataSource = new MatTableDataSource(this.dataRow)
        //     this.dataSource.paginator = this._paginator;
        //     this.dataSource.sort = this._sort;
        //     this._changeDetectorRef.markForCheck();
        // })
        this.deadstock$ = this._Service.deadstock$;

        this._Service.getDashboardData().subscribe((res: any) => {
            // this.data = res;

            this.data1 = res;
            this.data1.monthly_sales.forEach((element) => {
                this.month.push(element.month);
            });
            this.data1.monthly_sales.forEach((element) => {
                this.total.push(element.total_sales);
            });

            this.data1.return_shipments.forEach((element) => {
                this.monthreturn.push(element.month);
            });
            this.data1.return_shipments.forEach((element) => {
                this.totalreturn.push(element.return_shipments);
            });
        });
    }
    /**
     * After view init
     */
    // barChartMethod() {
    //     this._ngZone.runOutsideAngular(() => {
    //         this.barChart = new Chart(this.barCanvas?.nativeElement, {
    //             type: 'bar',
    //             data: {
    //                 labels: this.monthreturn,
    //                 datasets: [
    //                     {
    //                         label: 'ส่งสินค้าไม่สำเร็จ',
    //                         data: this.totalreturn,
    //                         backgroundColor: [
    //                             'rgba(31, 41, 55, 0.8)', // สีเทาเข้ม (เข้ากับพื้นหลัง)
    //                             'rgba(34, 197, 94, 0.7)', // สีเขียวสะท้อนแสง
    //                             'rgba(79, 70, 229, 0.7)', // สีฟ้าเข้ม
    //                             'rgba(236, 72, 153, 0.7)', // สีชมพูฟ้า
    //                             'rgba(96, 165, 250, 0.7)', // สีฟ้าอ่อน
    //                             'rgba(59, 130, 246, 0.7)', // สีฟ้าน้ำเงิน
    //                         ],
    //                         borderColor: [
    //                             'rgba(31, 41, 55, 1)', // สีเทาเข้ม (เข้ากับพื้นหลัง)
    //                             'rgba(34, 197, 94, 1)', // สีเขียวสะท้อนแสง
    //                             'rgba(79, 70, 229, 1)', // สีฟ้าเข้ม
    //                             'rgba(236, 72, 153, 1)', // สีชมพูฟ้า
    //                             'rgba(96, 165, 250, 1)', // สีฟ้าอ่อน
    //                             'rgba(59, 130, 246, 1)', // สีฟ้าน้ำเงิน
    //                         ],
    //                         borderWidth: 1,
    //                     },
    //                 ],
    //             },
    //             options: {
    //                 scales: {
    //                     y: {
    //                         beginAtZero: true,
    //                         ticks: {
    //                             color: 'rgba(255, 255, 255, 0.7)', // สีขาวโปร่งใสสำหรับตัวเลข Y-Axis
    //                         },
    //                     },
    //                     x: {
    //                         ticks: {
    //                             color: 'rgba(255, 255, 255, 0.7)', // สีขาวโปร่งใสสำหรับตัวเลข X-Axis
    //                         },
    //                     },
    //                 },
    //                 plugins: {
    //                     legend: {
    //                         labels: {
    //                             color: 'rgba(255, 255, 255, 0.8)', // สีขาวสำหรับข้อความ Legend
    //                         },
    //                     },
    //                 },
    //             },
    //         });
    //     });
    //     this._changeDetectorRef.detectChanges();
    // }

    // lineChartMethod() {
    //     this.lineChart = new Chart(this.lineCanvas?.nativeElement, {
    //         type: 'line',
    //         data: {
    //             labels: this.month,
    //             datasets: [
    //                 {
    //                     label: 'ยอดขายต่อเดือน',
    //                     fill: false,
    //                     backgroundColor: 'rgba(255, 99, 132, 0.2)', // สีชมพูใส
    //                     borderColor: 'rgba(255, 99, 132, 1)', // สีชมพูเข้ม
    //                     borderCapStyle: 'round',
    //                     borderDash: [],
    //                     borderDashOffset: 0.0,
    //                     borderJoinStyle: 'round',
    //                     pointBorderColor: 'rgba(255, 99, 132, 1)', // สีชมพูเข้ม
    //                     pointBackgroundColor: '#fff', // จุดสีขาว
    //                     pointBorderWidth: 2,
    //                     pointHoverRadius: 6,
    //                     pointHoverBackgroundColor: 'rgba(54, 162, 235, 1)', // สีน้ำเงินตอน hover
    //                     pointHoverBorderColor: 'rgba(54, 162, 235, 0.8)', // สีขอบตอน hover
    //                     pointHoverBorderWidth: 2,
    //                     pointRadius: 4,
    //                     pointHitRadius: 10,
    //                     data: this.total,
    //                     spanGaps: true, // ให้กราฟไม่ขาดตอน
    //                 },
    //             ],
    //         },
    //         options: {
    //             scales: {
    //                 x: {
    //                     grid: {
    //                         color: 'rgba(255, 255, 255, 0.1)', // เส้น grid สีขาวใส
    //                     },
    //                     ticks: {
    //                         color: '#ffffff', // สีตัวหนังสือแกน X
    //                     },
    //                 },
    //                 y: {
    //                     grid: {
    //                         color: 'rgba(255, 255, 255, 0.1)', // เส้น grid สีขาวใส
    //                     },
    //                     ticks: {
    //                         color: '#ffffff', // สีตัวหนังสือแกน Y
    //                     },
    //                 },
    //             },
    //             plugins: {
    //                 legend: {
    //                     labels: {
    //                         color: '#ffffff', // สีตัวหนังสือของ label
    //                     },
    //                 },
    //             },
    //         },
    //     });
    // }


    ngAfterViewInit(): void {
        // setTimeout(() => {
        //     this.barChartMethod();
        // }, 1000);

        // setTimeout(() => {
        //     this.lineChartMethod();
        // }, 1000); // หน่วงเวลา 1 วินาที
    }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    resetForm(): void {
        this.filterForm.reset();
        this.filterForm.get('asset_type').setValue('default');
        this._changeDetectorRef.markForCheck();
    }

    /**
     * Close the details
     */
    closeDetails(): void {
        this.selectedProduct = null;
    }

    /**
     * Show flash message
     */
    showFlashMessage(type: 'success' | 'error'): void {
        // Show the message
        this.flashMessage = type;

        // Mark for check
        this._changeDetectorRef.markForCheck();

        // Hide it after 3 seconds
        setTimeout(() => {
            this.flashMessage = null;

            // Mark for check
            this._changeDetectorRef.markForCheck();
        }, 3000);
    }

    callDetail(productId: string): void {
        // alert(this.selectedProduct.id);
        // // If the product is already selected...
        // if (this.selectedProduct && this.selectedProduct.id === productId) {
        //     // Close the details
        //     // this.closeDetails();
        //     return;
        // }

        this._router.navigate(['marketing/brief-plan/' + productId]);
    }

    edit(branchId: string): void {
        this._router.navigate(['branch/edit/' + branchId]);
    }

    openNewBrief(): void {
        this._router.navigateByUrl('marketing/brief-plan/brief/create');
    }

    openNewOrder(productId: string): void {
        // If the product is already selected...
        // if (this.selectedProduct && this.selectedProduct.id === productId) {
        //     // Close the details
        //     // this.closeDetails();
        //     return;
        // }

        this._router.navigate([
            'marketing/data/assets-list/new-order/' + productId,
        ]);
    }

    textStatus(status: string): string {
        return startCase(status);
    }

    openDialog() {
        // const dialogRef = this._matDialog.open(NewBranchComponent);
    }

    // openImportOsm(): void {
    //     this._matDialog.open(ImportOSMComponent)
    // }

    pages = { current_page: 1, last_page: 1, per_page: 10, begin: 0 };
    loadTable(): void {
        const that = this;
        this.dtOptions = {
            pagingType: 'full_numbers',
            pageLength: 10,
            serverSide: true,
            processing: true,
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.11.3/i18n/th.json',
            },
            ajax: (dataTablesParameters: any, callback) => {
                that._Service
                    .getBranchPage(dataTablesParameters)
                    .subscribe((resp) => {
                        this.dataRow = resp.data;
                        this.pages.current_page = resp.current_page;
                        this.pages.last_page = resp.last_page;
                        this.pages.per_page = resp.per_page;
                        if (resp.current_page > 1) {
                            this.pages.begin =
                                resp.per_page * resp.current_page - 1;
                        } else {
                            this.pages.begin = 0;
                        }
                        callback({
                            recordsTotal: resp.total,
                            recordsFiltered: resp.total,
                            data: [],
                        });
                        this._changeDetectorRef.markForCheck();
                    });
            },
            columns: [
                { data: 'id' },
                { data: 'name' },
                { data: 'status' },
                { data: 'create_by' },
                { data: 'created_at' },
                { data: 'actice', orderable: false },
            ],
        };
    }
}
